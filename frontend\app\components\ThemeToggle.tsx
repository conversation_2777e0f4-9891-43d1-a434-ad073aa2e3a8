'use client';

import React from 'react';
import { motion } from 'motion/react';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  className?: string;
}

export default function ThemeToggle({ className = '' }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();

  return (
    <motion.button
      onClick={toggleTheme}
      className={`relative flex items-center justify-center w-12 h-6 rounded-full transition-all duration-300 ${
        theme === 'dark' 
          ? 'bg-kairosGray3' 
          : 'bg-kairosGray1'
      } ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {/* Toggle circle */}
      <motion.div
        className={`absolute w-5 h-5 rounded-full flex items-center justify-center ${
          theme === 'dark' 
            ? 'bg-kairosRed' 
            : 'bg-white'
        }`}
        animate={{
          x: theme === 'dark' ? 24 : 0,
        }}
        transition={{
          type: 'spring',
          stiffness: 500,
          damping: 30,
        }}
      >
        {/* Icon */}
        <motion.div
          initial={false}
          animate={{
            scale: 1,
            rotate: theme === 'dark' ? 0 : 180,
          }}
          transition={{
            duration: 0.3,
          }}
        >
          {theme === 'dark' ? (
            <MoonIcon className="w-3 h-3 text-kairosGray4" />
          ) : (
            <SunIcon className="w-3 h-3 text-kairosGray3" />
          )}
        </motion.div>
      </motion.div>
      
      {/* Background icons */}
      <div className="absolute inset-0 flex items-center justify-between px-1">
        <SunIcon className={`w-3 h-3 transition-opacity duration-300 ${
          theme === 'light' ? 'opacity-0' : 'opacity-50'
        } ${theme === 'dark' ? 'text-kairosRed' : 'text-kairosGray3'}`} />
        <MoonIcon className={`w-3 h-3 transition-opacity duration-300 ${
          theme === 'dark' ? 'opacity-0' : 'opacity-50'
        } ${theme === 'dark' ? 'text-kairosRed' : 'text-kairosGray3'}`} />
      </div>
    </motion.button>
  );
}
