'use client';

import React from 'react';
import { motion } from 'motion/react';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  className?: string;
}

export default function ThemeToggle({ className = '' }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();

  return (
    <motion.button
      onClick={toggleTheme}
      className={`relative flex items-center justify-center w-16 h-8 rounded-full transition-all duration-500 shadow-lg ${
        theme === 'dark'
          ? 'bg-gradient-to-r from-muted to-foreground shadow-muted/30'
          : 'bg-gradient-to-r from-border to-secondary shadow-border/30 border border-border/50'
      } ${className}`}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {/* Toggle circle - Apple-like */}
      <motion.div
        className={`absolute w-7 h-7 rounded-full flex items-center justify-center shadow-xl ${
          theme === 'dark'
            ? 'bg-gradient-to-br from-primary via-secondary to-primary border border-primary/20'
            : 'bg-gradient-to-br from-white via-primary/10 to-white border border-border shadow-lg'
        }`}
        animate={{
          x: theme === 'dark' ? 32 : 0,
        }}
        transition={{
          type: 'spring',
          stiffness: 800,
          damping: 35,
        }}
      >
        {/* Icon with smooth transition */}
        <motion.div
          initial={false}
          animate={{
            scale: 1,
            rotate: theme === 'dark' ? 0 : 180,
          }}
          transition={{
            duration: 0.5,
            ease: 'easeInOut'
          }}
        >
          {theme === 'dark' ? (
            <MoonIcon className="w-4 h-4 text-foreground" />
          ) : (
            <SunIcon className="w-4 h-4 text-muted" />
          )}
        </motion.div>
      </motion.div>

      {/* Background icons with better positioning */}
      <div className="absolute inset-0 flex items-center justify-between px-2.5">
        <motion.div
          animate={{
            opacity: theme === 'light' ? 0 : 0.6,
            scale: theme === 'light' ? 0.8 : 1
          }}
          transition={{ duration: 0.4 }}
        >
          <SunIcon className="w-3.5 h-3.5 text-primary/70" />
        </motion.div>
        <motion.div
          animate={{
            opacity: theme === 'dark' ? 0 : 0.6,
            scale: theme === 'dark' ? 0.8 : 1
          }}
          transition={{ duration: 0.4 }}
        >
          <MoonIcon className="w-3.5 h-3.5 text-muted/70" />
        </motion.div>
      </div>
    </motion.button>
  );
}
