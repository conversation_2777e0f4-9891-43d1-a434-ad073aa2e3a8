'use client';

import React from 'react';
import { motion } from 'motion/react';
import { SunIcon, MoonIcon } from '@heroicons/react/24/outline';
import { useTheme } from '../contexts/ThemeContext';

interface ThemeToggleProps {
  className?: string;
}

export default function ThemeToggle({ className = '' }: ThemeToggleProps) {
  const { theme, toggleTheme } = useTheme();

  return (
    <motion.button
      onClick={toggleTheme}
      className={`relative flex items-center justify-center w-14 h-7 rounded-full transition-all duration-300 shadow-lg ${
        theme === 'dark'
          ? 'bg-gradient-to-r from-muted to-accent shadow-muted/20'
          : 'bg-gradient-to-r from-secondary to-accent shadow-secondary/20 border border-border'
      } ${className}`}
      whileHover={{ scale: 1.05, shadow: '0 8px 25px rgba(0,0,0,0.15)' }}
      whileTap={{ scale: 0.95 }}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {/* Toggle circle */}
      <motion.div
        className={`absolute w-6 h-6 rounded-full flex items-center justify-center shadow-md ${
          theme === 'dark'
            ? 'bg-gradient-to-br from-primary to-secondary'
            : 'bg-gradient-to-br from-white to-primary border border-border'
        }`}
        animate={{
          x: theme === 'dark' ? 28 : 0,
        }}
        transition={{
          type: 'spring',
          stiffness: 700,
          damping: 30,
        }}
      >
        {/* Icon */}
        <motion.div
          initial={false}
          animate={{
            scale: 1,
            rotate: theme === 'dark' ? 0 : 180,
          }}
          transition={{
            duration: 0.4,
            ease: 'easeInOut'
          }}
        >
          {theme === 'dark' ? (
            <MoonIcon className="w-3.5 h-3.5 text-muted" />
          ) : (
            <SunIcon className="w-3.5 h-3.5 text-accent" />
          )}
        </motion.div>
      </motion.div>

      {/* Background icons */}
      <div className="absolute inset-0 flex items-center justify-between px-2">
        <motion.div
          animate={{ opacity: theme === 'light' ? 0 : 0.4 }}
          transition={{ duration: 0.3 }}
        >
          <SunIcon className="w-3 h-3 text-primary" />
        </motion.div>
        <motion.div
          animate={{ opacity: theme === 'dark' ? 0 : 0.4 }}
          transition={{ duration: 0.3 }}
        >
          <MoonIcon className="w-3 h-3 text-accent" />
        </motion.div>
      </div>
    </motion.button>
  );
}
