'use client';

import React, { useState, useEffect } from 'react';
import <PERSON> from 'next/link';
import { ArrowRightIcon, ChartBarIcon, ClockIcon, CodeBracketIcon, LightBulbIcon } from '@heroicons/react/24/outline';
import { CalendarDaysIcon } from '@heroicons/react/24/solid';
import { motion } from 'motion/react';
import FontSwitcher from '../../components/FontSwitcher';

// Custom components
import ModernNavbar from '../components/landing/ModernNavbar';
import LandingBackgroundBlobs from '../components/LandingBackgroundBlobs';
import DemoChart from '../components/landing/DemoChart';
import GrainOverlay from '../components/GrainOverlay';
import VignetteEffect from '../components/VignetteEffect';
import ParallaxSection from '../components/ParallaxSection';
import AnimatedHeroSVG from '../components/landing/AnimatedHeroSVG';

const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

export default function LandingPage() {
  const [email, setEmail] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const res = await fetch(`${BACKEND_API_URL}/waitlist`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      if (res.ok) {
        alert('Thanks for joining our waitlist! We\'ll be in touch soon.');
        setEmail('');
      } else {
        const data = await res.json();
        alert(data.detail || 'There was an error. Please try again.');
      }
    } catch (err) {
      alert('There was an error. Please try again.');
    }
  };

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.5 }
    }
  };

  return (
    <FontSwitcher>
      <LandingBackgroundBlobs />
      <GrainOverlay />
      <VignetteEffect />
      <ModernNavbar />

      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-20">
        <div className="max-w-7xl mx-auto w-full">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Text content */}
            <motion.div
              className="text-left"
              initial="hidden"
              animate="visible"
              variants={containerVariants}
            >
              <motion.h1
                className="text-4xl md:text-6xl font-bold mb-6 text-foreground leading-tight"
                variants={itemVariants}
              >
                <span className="text-foreground">Agents that build world models for </span>
                <span className="text-primary">forecasting</span>
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-muted mb-10 max-w-2xl"
                variants={itemVariants}
              >
                A powerful agentic forecasting system that goes beyond traditional time series models by intelligently gathering context and simulating scenarios, saving businesses hours of manual work and cost reduction.
              </motion.p>

              {/* Waitlist Form */}
              <motion.div
                className="max-w-md"
                variants={itemVariants}
                id="waitlist"
              >
                <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-3">
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    required
                    className="flex-grow px-4 py-3 rounded-full bg-secondary/20 border border-border text-foreground placeholder-muted focus:outline-none focus:ring-2 focus:ring-primary backdrop-blur-sm"
                  />
                  <motion.button
                    type="submit"
                    className="glow-button px-6 py-3 bg-primary text-foreground font-bold rounded-full hover:bg-secondary transition-colors shadow-lg border-2 border-transparent hover:border-border"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Join Waitlist
                  </motion.button>
                </form>
              </motion.div>
            </motion.div>

            {/* Right side - Animated SVG */}
            <motion.div
              className="flex justify-center lg:justify-end"
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.3 }}
            >
              <AnimatedHeroSVG />
            </motion.div>
          </div>
        </div>

        {/* Scroll indicator */}
        <motion.div
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1, duration: 1 }}
        >
          <motion.div
            animate={{ y: [0, 10, 0] }}
            transition={{ repeat: Infinity, duration: 1.5 }}
            onClick={() => {
              const el = document.getElementById('demo');
              if (el) {
                const yOffset = -40; // Less negative offset for more downward scroll
                const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;
                window.scrollTo({ top: y, behavior: 'smooth' });
              }
            }}
            style={{ cursor: 'pointer' }}
          >
            <ArrowRightIcon className="h-6 w-6 text-white rotate-90" />
          </motion.div>
        </motion.div>
      </section>

      {/* Vision Section */}
      <ParallaxSection speed={0.3}>
        <section id="about" className="py-20 px-4 sm:px-6 lg:px-8">
          <motion.div
            className="max-w-5xl mx-auto"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true, amount: 0.3 }}
            transition={{ duration: 0.5 }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-bold mb-12 text-white text-center text-shadow-glow"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7 }}
            >
              Our <span className="text-red-400">Vision</span>
            </motion.h2>

            <motion.div
              className="glass-card p-8 md:p-10"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.7, delay: 0.2 }}
            >
              <div className="grid md:grid-cols-2 gap-10">
                <motion.div
                  initial={{ x: -50, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.7, delay: 0.4 }}
                >
                  <h3 className="text-xl text-white mb-4">Context & Causality</h3>
                  <p className="text-gray-300">
                    To build intelligent agents that understand context and causality to power real-world forecasting and decision-making
                  </p>
                </motion.div>
                <motion.div
                  initial={{ x: 50, opacity: 0 }}
                  whileInView={{ x: 0, opacity: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.7, delay: 0.6 }}
                >
                  <h3 className="text-xl text-primary mb-4">Time as a Core Dimension</h3>
                  <p className="text-muted">
                    Build a foundational time series model that treats time as a core dimension, so that machines can reason over contexts, simulate dynamic futures, and enable long-term decision intelligence.
                  </p>
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </section>
      </ParallaxSection>

      {/* Features Section */}
      <section id="features" className="py-20 px-4 sm:px-6 lg:px-8 bg-secondary/10 backdrop-blur-sm">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-16 text-foreground text-center"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            Powerful <span className="text-primary">Features</span>
          </motion.h2>

          <div className="grid md:grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Feature 1 */}
            <motion.div
              className="glass-card p-6 flex flex-col h-full border-t-2 border-accent"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
              whileHover={{ y: -10, transition: { duration: 0.2 } }}
            >
              <div className="mb-4 text-accent">
                <ChartBarIcon className="h-10 w-10" />
              </div>
              <h3 className="text-xl text-foreground mb-3">Gathers and processes both structured (time series) and unstructured (text, news, web) data to generate accurate, real-world forecasts.</h3>
            </motion.div>
            {/* Feature 2 */}
            <motion.div
              className="glass-card p-6 flex flex-col h-full border-t-2 border-secondary"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
              whileHover={{ y: -10, transition: { duration: 0.2 } }}
            >
              <div className="mb-4 text-secondary">
                <CodeBracketIcon className="h-10 w-10" />
              </div>
              <h3 className="text-xl text-foreground mb-3">Performs causal discovery and provides transparent reasoning through explainability graphs and traceable logic.</h3>
            </motion.div>
            {/* Feature 3 */}
            <motion.div
              className="glass-card p-6 flex flex-col h-full border-t-2 border-primary"
              initial={{ y: 50, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
              whileHover={{ y: -10, transition: { duration: 0.2 } }}
            >
              <div className="mb-4 text-primary">
                <LightBulbIcon className="h-10 w-10" />
              </div>
              <h3 className="text-xl text-foreground mb-3">Combines time series foundation models with a user-facing dashboard for tailored insights, scenario simulations, and business-specific agent templates.</h3>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* Demo Section */}
      <section id="demo" className="py-20 px-4 sm:px-6 lg:px-8">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-12 text-foreground text-center"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            See Kairos <span className="text-primary">in Action</span>
          </motion.h2>

          <motion.div
            className="glass-card p-6 md:p-10 overflow-hidden"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            {/* Interactive demo chart */}
            <div className="grid  gap-8 ">
              <motion.div
                className="bg-secondary/20 rounded-lg backdrop-blur-sm"
                initial={{ x: 50, opacity: 0 }}
                whileInView={{ x: 0, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.7, delay: 0.4 }}
                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
              >
                <DemoChart />
              </motion.div>
            </div>

            <motion.div
              className="mt-8 text-center"
              initial={{ y: 30, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {/* <Link href="/chat" className="inline-block px-8 py-4 bg-red-400 text-white font-bold rounded-full hover:bg-red-500 transition-colors shadow-lg shadow-red-400/40 border-2 border-transparent hover:border-white/20 text-lg">
                  Try the Demo
                </Link> */}
              </motion.div>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>

      {/* Talk to Founders Section */}
      <section id="founders" className="py-20 px-4 sm:px-6 lg:px-8 bg-black/30 backdrop-blur-sm">
        <motion.div
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          <motion.h2
            className="text-3xl md:text-4xl font-bold mb-6 text-white"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7 }}
          >
            Talk to Our <span className="text-red-400">Founders</span>
          </motion.h2>
          <motion.p
            className="text-xl text-white mb-10"
            initial={{ y: 30, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.2 }}
          >
            Schedule a personalized demo and discover how Kairos can transform your credit risk assessment
          </motion.p>

          <motion.div
            className="glass-card p-8 md:p-10 inline-block"
            initial={{ y: 50, opacity: 0 }}
            whileInView={{ y: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.3 }}
            whileHover={{
              y: -5,
               transition: { duration: 0.2 }
            }}
          >
            <div className="flex items-center justify-center gap-4">
              <motion.div
                initial={{ rotate: 0 }}
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
              >
                <CalendarDaysIcon className="h-10 w-10 text-red-400" />
              </motion.div>
              <motion.a
                href="https://calendly.com/jajoo-kairosai/30min"
                target="_blank"
                rel="noopener noreferrer"
                className="px-8 py-4 bg-red-400 text-white font-bold rounded-full hover:bg-red-500 transition-colors shadow-lg shadow-red-400/30 border-2 border-transparent hover:border-white/20 text-lg"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                Schedule a Meeting
              </motion.a>
            </div>
          </motion.div>

          <motion.div
            className="mt-8 text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.7, delay: 0.5 }}
          >
            {/* Backed by <span className="text-red-400 font-medium">Y Combinator</span>} */}
          </motion.div>
        </motion.div>
      </section>

      {/* Footer */}
      <footer className="py-12 px-4 sm:px-6 lg:px-8 border-t border-border">
        <motion.div
          className="max-w-6xl mx-auto"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <h3 className="text-lg text-foreground mb-4">Kairos</h3>
              <p className="text-muted">
              Trying to teach models what Einstein taught physics that time isn't just a label, it's a dimension!
              </p>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <h3 className="text-lg text-foreground mb-4">Links</h3>
              <ul className="space-y-2">
                <li><Link href="/" className="text-muted hover:text-primary transition-colors">Home</Link></li>
                <li><Link href="/about" className="text-muted hover:text-primary transition-colors">About</Link></li>
                <li><Link href="/contact" className="text-muted hover:text-primary transition-colors">Contact</Link></li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <h3 className="text-lg text-foreground mb-4">Legal</h3>
              <ul className="space-y-2">
                <li><Link href="/privacy" className="text-muted hover:text-primary transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="text-muted hover:text-primary transition-colors">Terms of Service</Link></li>
              </ul>
            </motion.div>

            <motion.div
              initial={{ y: 20, opacity: 0 }}
              whileInView={{ y: 0, opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <h3 className="text-lg  text-white mb-4">Connect</h3>
              <div className="flex space-x-4">
                <motion.a
                  href="https://x.com/kairos_ai__"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-kairosYellow transition-colors"
                  whileHover={{ scale: 1.2, color: "#fff68d" }}
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84" />
                  </svg>
                </motion.a>
                <motion.a
                  href="https://www.linkedin.com/company/kairos-ai/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-kairosYellow transition-colors"
                  whileHover={{ scale: 1.2, color: "#fff68d" }}
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z" />
                  </svg>
                </motion.a>
                <motion.a
                  href="http://www.github.com/KairosAI-IN"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-400 hover:text-kairosYellow transition-colors"
                  whileHover={{ scale: 1.2, color: "#fff68d" }}
                >
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path fillRule="evenodd" d="M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z" clipRule="evenodd" />
                  </svg>
                </motion.a>
              </div>
            </motion.div>
          </div>

          <motion.div
            className="mt-12 pt-8 border-t border-white/10 text-center"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            <p className="text-gray-400">
              &copy; {new Date().getFullYear()} Kairos AI. All rights reserved.
            </p>
          </motion.div>
        </motion.div>
      </footer>
    </FontSwitcher>
  );
}




