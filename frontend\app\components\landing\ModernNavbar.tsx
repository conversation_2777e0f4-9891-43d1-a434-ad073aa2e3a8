'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion } from 'motion/react';
import ThemeToggle from '../ThemeToggle';
import { useTheme } from '../../contexts/ThemeContext';

export default function ModernNavbar() {
  const pathname = usePathname();
  const { theme } = useTheme();
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  // Handle scroll effect for navbar
  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      if (offset > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Enable smooth scrolling for anchor links
    const originalScrollBehavior = document.documentElement.style.scrollBehavior;
    document.documentElement.style.scrollBehavior = 'smooth';

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.documentElement.style.scrollBehavior = originalScrollBehavior;
    };
  }, []);

  const isActive = (path: string) => {
    return pathname === path;
  };

  // Helper for smooth scroll with offset
  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {
    e.preventDefault();
    if (id === 'top') {
      window.scrollTo({ top: 0, behavior: 'smooth' });
      return;
    }
    const el = document.getElementById(id);
    if (el) {
      const yOffset = -100; // Adjust this value to match your navbar height
      const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;
      window.scrollTo({ top: y, behavior: 'smooth' });
    }
  };

  return (
    <motion.nav
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`fixed max-w-7xl left-1/2 -translate-x-1/2 z-60 transition-all duration-300 ${
        scrolled
          ? 'py-3 mx-auto my-3 bg-background/80 backdrop-blur-lg rounded-full border border-border/20'
          : 'py-5 mx-auto bg-transparent'
      }`}
    >
      <div className="max-w-7xl mx-auto px-2 sm:px-3 lg:px-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <Link href="/" className="flex items-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="flex items-center"
            >
              <span className="text-2xl font-bold text-foreground font-inknut">
                Kairos
              </span>
              <span className="ml-1 text-kairosGreen text-2xl">AI</span>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex space-x-6 ml-12"
            >
              {[
                { name: 'About', path: '#about' },
                { name: 'Features', path: '#features' },
                { name: 'Founders', path: '#founders' },
              ].map((item, index) => (
                <a
                  key={item.path}
                  href={item.path}
                  onClick={item.path.startsWith('#') ? (e) => handleSmoothScroll(e, item.path.substring(1)) : undefined}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
                    isActive(item.path)
                      ? 'text-foreground bg-primary'
                      : 'text-muted hover:text-foreground hover:bg-secondary/20'
                  }`}
                >
                  {item.name}
                </a>
              ))}
            </motion.div>

            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              className="ml-4 flex items-center space-x-3"
            >
              <ThemeToggle />
              <a
                href="#top"
                onClick={(e) => handleSmoothScroll(e, 'top')}
                className="px-4 py-2 bg-primary text-foreground rounded-full hover:bg-secondary transition-all duration-300 shadow-lg font-medium cursor-pointer"
              >
                Get Started
              </a>
            </motion.div>
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden flex items-center">
            <button
              type="button"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="inline-flex items-center justify-center p-2 rounded-full text-gray-300 hover:text-white focus:outline-none bg-black/50 backdrop-blur-sm border border-white/10"
              aria-expanded="false"
            >
              <span className="sr-only">Open main menu</span>
              {/* Icon when menu is closed */}
              <svg
                className={`${mobileMenuOpen ? 'hidden' : 'block'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M4 6h16M4 12h16M4 18h16"
                />
              </svg>
              {/* Icon when menu is open */}
              <svg
                className={`${mobileMenuOpen ? 'block' : 'hidden'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                aria-hidden="true"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Mobile menu, show/hide based on menu state */}
      <motion.div
        initial={{ opacity: 0, height: 0 }}
        animate={{
          opacity: mobileMenuOpen ? 1 : 0,
          height: mobileMenuOpen ? 'auto' : 0,
        }}
        transition={{ duration: 0.3 }}
        className={`md:hidden overflow-visible flex justify-center items-start w-full fixed left-0 top-20 z-50`}
        style={{ pointerEvents: mobileMenuOpen ? 'auto' : 'none' }}
      >
        <div className="relative  w-72 max-w-[90vw] bg-black/90 backdrop-blur-lg rounded-3xl border border-white/10 shadow-2xl flex flex-col items-center px-4 py-6">
          {/* Removed close button from inside the menu */}
          <div className="flex flex-col space-y-3 w-full mt-6">
            {[
              { name: 'About', path: '#about' },
              { name: 'Features', path: '#features' },
              { name: 'Founders', path: '#founders' },
            ].map((item) => (
              <a
                key={item.path}
                href={item.path}
                onClick={item.path.startsWith('#') ? (e) => { handleSmoothScroll(e, item.path.substring(1)); setMobileMenuOpen(false); } : undefined}
                className={`block px-3 py-2 rounded-md text-base font-medium transition-all duration-200 ${
                  isActive(item.path)
                    ? 'text-black bg-kairosYellow'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                {item.name}
              </a>
            ))}
            <a
              href="#top"
              onClick={(e) => { handleSmoothScroll(e, 'top'); setMobileMenuOpen(false); }}
              className="block w-full mt-4 px-3 py-2 bg-kairosBlue text-white rounded-full hover:bg-kairosBlue/90 transition-colors text-center font-medium shadow-lg shadow-kairosBlue/20 cursor-pointer"
            >
              Get Started
            </a>
          </div>
        </div>
      </motion.div>
    </motion.nav>
  );
}







