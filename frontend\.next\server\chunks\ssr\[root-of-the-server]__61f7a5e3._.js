module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[next]/internal/font/google/inter_cb69f1e2.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "inter_cb69f1e2-module__bRYYAW__className",
  "variable": "inter_cb69f1e2-module__bRYYAW__variable",
});
}}),
"[next]/internal/font/google/inter_cb69f1e2.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_cb69f1e2.module.css [app-ssr] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inter', 'Inter Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/inknut_antiqua_8bb50917.module.css [app-ssr] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "inknut_antiqua_8bb50917-module__p2dL8W__className",
  "variable": "inknut_antiqua_8bb50917-module__p2dL8W__variable",
});
}}),
"[next]/internal/font/google/inknut_antiqua_8bb50917.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inknut_antiqua_8bb50917.module.css [app-ssr] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inknut Antiqua', 'Inknut Antiqua Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$module$2e$css__$5b$app$2d$ssr$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/app/fonts.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_cb69f1e2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inknut_antiqua_8bb50917.js [app-ssr] (ecmascript)");
;
;
;
;
}}),
"[project]/app/fonts.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_cb69f1e2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inknut_antiqua_8bb50917.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$fonts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/app/fonts.ts [app-ssr] (ecmascript) <locals>");
}}),
"[next]/internal/font/google/inter_cb69f1e2.js [app-ssr] (ecmascript) <export default as inter>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "inter": (()=>__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_cb69f1e2.js [app-ssr] (ecmascript)");
}}),
"[next]/internal/font/google/inknut_antiqua_8bb50917.js [app-ssr] (ecmascript) <export default as inknutAntiqua>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "inknutAntiqua": (()=>__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inknut_antiqua_8bb50917.js [app-ssr] (ecmascript)");
}}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/app/contexts/ThemeContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider),
    "useTheme": (()=>useTheme)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const ThemeContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useTheme() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ThemeContext);
    if (context === undefined) {
        throw new Error('useTheme must be used within a ThemeProvider');
    }
    return context;
}
function ThemeProvider({ children }) {
    const [theme, setThemeState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('light');
    // Initialize theme from localStorage or system preference
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const savedTheme = localStorage.getItem('kairos-theme');
        if (savedTheme) {
            setThemeState(savedTheme);
        } else {
            // Check system preference
            const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            setThemeState(systemPrefersDark ? 'dark' : 'light');
        }
    }, []);
    // Apply theme to document
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const root = document.documentElement;
        const body = document.body;
        if (theme === 'dark') {
            root.classList.add('dark');
            body.style.background = '#000000';
            root.style.setProperty('--background', '#000000');
            root.style.setProperty('--foreground', '#ffffff');
            root.style.setProperty('--primary', '#f2e9e4');
            root.style.setProperty('--secondary', '#c9ada7');
            root.style.setProperty('--accent', '#9a8c98');
            root.style.setProperty('--muted', '#4a4e69');
            root.style.setProperty('--border', '#22223b');
        } else {
            root.classList.remove('dark');
            body.style.background = '#ffffff';
            root.style.setProperty('--background', '#ffffff');
            root.style.setProperty('--foreground', '#22223b');
            root.style.setProperty('--primary', '#f2e9e4');
            root.style.setProperty('--secondary', '#c9ada7');
            root.style.setProperty('--accent', '#9a8c98');
            root.style.setProperty('--muted', '#4a4e69');
            root.style.setProperty('--border', '#c9ada7');
        }
    }, [
        theme
    ]);
    const setTheme = (newTheme)=>{
        setThemeState(newTheme);
        localStorage.setItem('kairos-theme', newTheme);
    };
    const toggleTheme = ()=>{
        const newTheme = theme === 'light' ? 'dark' : 'light';
        setTheme(newTheme);
    };
    const value = {
        theme,
        toggleTheme,
        setTheme
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ThemeContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/app/contexts/ThemeContext.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
}
}}),
"[project]/app/components/BackgroundBlobs.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// components/BackgroundBlobs.tsx
__turbopack_context__.s({
    "default": (()=>BackgroundBlobs)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$ThemeContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/contexts/ThemeContext.tsx [app-ssr] (ecmascript)");
'use client';
;
;
// XYBlob component for positioning blobs anywhere
function XYBlob({ x, y, size, color, opacity = 0.7, blur = "80px", mirror = false }) {
    // Create gradient based on color
    const getGradient = ()=>{
        const rgba = hexToRgba(color, 0.4);
        return `radial-gradient(circle at 50% 50%, ${color} 0%, ${rgba} 30%, transparent 70%)`;
    };
    // Helper to convert hex to rgba
    const hexToRgba = (hex, alpha)=>{
        const r = parseInt(hex.slice(1, 3), 16);
        const g = parseInt(hex.slice(3, 5), 16);
        const b = parseInt(hex.slice(5, 7), 16);
        return `rgba(${r},${g},${b},${alpha})`;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "pointer-events-none fixed -z-40 rounded-full mix-blend-screen",
        style: {
            left: x,
            top: y,
            width: size,
            height: size,
            opacity: opacity,
            background: getGradient(),
            filter: `blur(${blur})`
        },
        children: mirror && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "absolute inset-0 scale-x-[-1]",
            style: {
                background: "inherit"
            }
        }, void 0, false, {
            fileName: "[project]/app/components/BackgroundBlobs.tsx",
            lineNumber: 55,
            columnNumber: 9
        }, this)
    }, void 0, false, {
        fileName: "[project]/app/components/BackgroundBlobs.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, this);
}
function BackgroundBlobs() {
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$ThemeContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    // Define colors based on theme
    const blobColors = theme === 'dark' ? {
        primary: '#4C85F6',
        secondary: '#BCC5FF',
        accent: '#A020F0'
    } : {
        primary: '#c9ada7',
        secondary: '#9a8c98',
        accent: '#4a4e69'
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 -z-50 bg-background overflow-hidden"
            }, void 0, false, {
                fileName: "[project]/app/components/BackgroundBlobs.tsx",
                lineNumber: 83,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(XYBlob, {
                x: "-20%",
                y: "0%",
                size: "40vw",
                color: blobColors.primary,
                opacity: theme === 'dark' ? 0.7 : 0.3,
                blur: "150px"
            }, void 0, false, {
                fileName: "[project]/app/components/BackgroundBlobs.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(XYBlob, {
                x: "80%",
                y: "40%",
                size: "30vw",
                color: blobColors.secondary,
                opacity: theme === 'dark' ? 0.6 : 0.25,
                blur: "150px",
                mirror: true
            }, void 0, false, {
                fileName: "[project]/app/components/BackgroundBlobs.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(XYBlob, {
                x: "22%",
                y: "65%",
                size: "60vw",
                color: blobColors.accent,
                opacity: theme === 'dark' ? 0.5 : 0.2,
                blur: "60px"
            }, void 0, false, {
                fileName: "[project]/app/components/BackgroundBlobs.tsx",
                lineNumber: 104,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
}}),
"[project]/app/utils/websocket.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// WebSocket client utility for chat functionality
// Backend URL configuration - can be moved to environment variables later
__turbopack_context__.s({
    "BACKEND_API_URL": (()=>BACKEND_API_URL),
    "BACKEND_WS_URL": (()=>BACKEND_WS_URL),
    "WebSocketClient": (()=>WebSocketClient),
    "uploadFile": (()=>uploadFile),
    "websocketClient": (()=>websocketClient)
});
const BACKEND_WS_URL = ("TURBOPACK compile-time value", "wss://localhost:8000/ws") || 'ws://localhost:8000/ws';
const BACKEND_API_URL = ("TURBOPACK compile-time value", "http://localhost:8000") || 'http://localhost:8000';
class WebSocketClient {
    socket = null;
    reconnectAttempts = 0;
    maxReconnectAttempts = 5;
    reconnectTimeout = null;
    messageQueue = [];
    onMessageCallback = null;
    onStatusChangeCallback = null;
    constructor(){
        // Initialize in a disconnected state
        this.notifyStatusChange('disconnected');
    }
    // Connect to the WebSocket server
    connect() {
        if (this.socket?.readyState === WebSocket.OPEN) {
            console.log('WebSocket already connected');
            return;
        }
        try {
            this.notifyStatusChange('connecting');
            this.socket = new WebSocket(BACKEND_WS_URL);
            this.socket.onopen = this.handleOpen.bind(this);
            this.socket.onmessage = this.handleMessage.bind(this);
            this.socket.onclose = this.handleClose.bind(this);
            this.socket.onerror = this.handleError.bind(this);
        } catch (error) {
            console.error('Failed to connect to WebSocket:', error);
            this.notifyStatusChange('error');
            this.attemptReconnect();
        }
    }
    // Disconnect from the WebSocket server
    disconnect() {
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
        if (this.reconnectTimeout) {
            clearTimeout(this.reconnectTimeout);
            this.reconnectTimeout = null;
        }
        this.notifyStatusChange('disconnected');
    }
    // Send a message to the WebSocket server
    sendMessage(message) {
        console.log("[DEBUG] Sending WebSocket message:", message);
        if (this.socket?.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
        } else {
            console.log('WebSocket not connected, queueing message');
            this.messageQueue.push(message);
            this.connect();
        }
    }
    // Set callback for incoming messages
    onMessage(callback) {
        this.onMessageCallback = callback;
    }
    // Set callback for connection status changes
    onStatusChange(callback) {
        this.onStatusChangeCallback = callback;
    }
    // Handle WebSocket open event
    handleOpen() {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        this.notifyStatusChange('connected');
        // Send any queued messages
        while(this.messageQueue.length > 0){
            const message = this.messageQueue.shift();
            if (message) {
                this.sendMessage(message);
            }
        }
    }
    // Handle WebSocket message event
    handleMessage(event) {
        try {
            console.log("[DEBUG] Raw WebSocket message received:", event.data);
            const data = JSON.parse(event.data);
            console.log("[DEBUG] Parsed WebSocket message:", data);
            if (this.onMessageCallback) {
                this.onMessageCallback(data);
            }
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    }
    // Handle WebSocket close event
    handleClose(event) {
        console.log(`WebSocket closed: ${event.code} ${event.reason}`);
        this.socket = null;
        this.notifyStatusChange('disconnected');
        this.attemptReconnect();
    }
    // Handle WebSocket error event
    handleError(event) {
        console.error('WebSocket error:', event);
        this.notifyStatusChange('error');
        this.socket?.close();
    }
    // Attempt to reconnect to the WebSocket server
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('Max reconnect attempts reached');
            return;
        }
        const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
        console.log(`Attempting to reconnect in ${delay}ms`);
        this.reconnectTimeout = setTimeout(()=>{
            this.reconnectAttempts++;
            this.connect();
        }, delay);
    }
    // Notify status change
    notifyStatusChange(status) {
        if (this.onStatusChangeCallback) {
            this.onStatusChangeCallback(status);
        }
    }
}
const websocketClient = new WebSocketClient();
async function uploadFile(file) {
    const formData = new FormData();
    formData.append('file', file);
    const response = await fetch(`${BACKEND_API_URL}/upload`, {
        method: 'POST',
        body: formData
    });
    if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
    }
    return response.json();
}
}}),
"[project]/app/contexts/ChatContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChatProvider": (()=>ChatProvider),
    "useChat": (()=>useChat)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/websocket.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
// Create the context with a default value
const ChatContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ChatProvider({ children }) {
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRouter"])();
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [thinking, setThinking] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [isThinking, setIsThinking] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [connectionStatus, setConnectionStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('disconnected');
    const [chartData, setChartData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [causalityData, setCausalityData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [chartSelection, setChartSelection] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({});
    const [uploadedFiles, setUploadedFiles] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [externalContexts, setExternalContexts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [selectedExternalContexts, setSelectedExternalContexts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [insights, setInsights] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("");
    const [currentSlug, setCurrentSlug] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Connect to WebSocket on component mount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        console.log("[DEBUG] ChatContext mounted");
        // Set up WebSocket event handlers
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["websocketClient"].onStatusChange((status)=>{
            console.log("[DEBUG] WebSocket status changed:", status);
            setConnectionStatus(status);
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["websocketClient"].onMessage((data)=>{
            console.log("[DEBUG] WebSocket message received in context:", data);
            handleWebSocketResponse(data);
        });
        // Connect to the WebSocket server - only if not already connected
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        } else {
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["websocketClient"].connect();
        }
        // Clean up on unmount
        return ()=>{
            console.log("[DEBUG] ChatContext unmounting, disconnecting WebSocket");
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["websocketClient"].disconnect();
        };
    }, []);
    // Handle WebSocket responses
    const handleWebSocketResponse = (data)=>{
        console.log("[DEBUG] WebSocket response received:", data);
        // Log all fields for debugging
        console.log("[DEBUG] Response fields:", {
            hasMessage: !!data.message,
            hasThought: !!data.thought,
            hasChart: !!data.chart,
            hasCausality: !!data.causality,
            hasInsights: !!data.insights,
            hasExternalContexts: !!data.external_contexts,
            hasHistory: !!data.history
        });
        // Handle slug if present
        if (data.slug) {
            console.log("[DEBUG] Slug received:", data.slug);
            setCurrentSlug(data.slug);
            // Update the URL without full page reload
            if ("TURBOPACK compile-time falsy", 0) {
                "TURBOPACK unreachable";
            }
        }
        // Handle chat history if present
        if (data.history && Array.isArray(data.history)) {
            console.log("[DEBUG] Chat history received:", data.history);
            // Log the structure of each message in history for debugging
            data.history.forEach((msg, idx)=>{
                console.log(`[DEBUG] History message ${idx}:`, {
                    type: msg.type,
                    hasChart: !!msg.chart,
                    hasCausality: !!msg.causality,
                    hasInsights: !!msg.insights,
                    hasExternalContexts: !!msg.external_contexts
                });
            });
            setMessages(data.history);
            // Extract latest data from history for charts, causality, insights, etc.
            // This ensures we have the latest data even if it's not sent separately
            const aiMessages = data.history.filter((msg)=>msg.type === 'ai');
            if (aiMessages.length > 0) {
                const latestAiMsg = aiMessages[aiMessages.length - 1];
                console.log("[DEBUG] Latest AI message from history:", latestAiMsg);
                // Always update chart data from the latest message in history
                // regardless of whether data.chart exists
                if (latestAiMsg.chart) {
                    console.log("[DEBUG] Setting chart data from history:", latestAiMsg.chart);
                    setChartData(latestAiMsg.chart);
                }
                // Always update causality data from the latest message in history
                if (latestAiMsg.causality) {
                    console.log("[DEBUG] Setting causality data from history:", latestAiMsg.causality);
                    setCausalityData(latestAiMsg.causality);
                } else {
                    console.log("[DEBUG] No causality data in latest message");
                }
                // Always update insights from the latest message in history
                if (latestAiMsg.insights) {
                    console.log("[DEBUG] Setting insights from history:", latestAiMsg.insights);
                    setInsights(latestAiMsg.insights);
                } else {
                    console.log("[DEBUG] No insights data in latest message");
                }
                // Always update external contexts from the latest message in history
                if (latestAiMsg.external_contexts) {
                    console.log("[DEBUG] Setting external contexts from history:", latestAiMsg.external_contexts);
                    const enriched = latestAiMsg.external_contexts.map((ctx, idx)=>({
                            ...ctx,
                            id: ctx.url + '-' + idx,
                            favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,
                            isSelected: true
                        }));
                    setExternalContexts(enriched);
                } else {
                    console.log("[DEBUG] No external contexts in latest message");
                }
            } else {
                console.log("[DEBUG] No AI messages found in history");
            }
        }
        if (data.thought) {
            console.log("[DEBUG] Processing thought:", data.thought);
            // Update thinking state
            setThinking(data.thought);
            setIsThinking(true);
        } else if (data.message) {
            console.log("[DEBUG] Processing message:", data.message);
            // Add AI message
            if (data.message) {
                // Create a complete message object with all available data
                const newMessage = {
                    type: 'ai',
                    content: data.message,
                    timestamp: new Date().toISOString(),
                    status: data.status || 'success'
                };
                // Add other fields if they exist in the response
                if (data.chart) newMessage.chart = data.chart;
                if (data.causality) newMessage.causality = data.causality;
                if (data.insights) newMessage.insights = data.insights;
                if (data.external_contexts) newMessage.external_contexts = data.external_contexts;
                if (data.thought) newMessage.thought = data.thought;
                // Update the state with the new message
                setMessages((prev)=>[
                        ...prev,
                        newMessage
                    ]);
                // Also update the individual state variables for each component
                // This ensures the UI components get the latest data
                if (data.chart) setChartData(data.chart);
                if (data.causality) setCausalityData(data.causality);
                if (data.insights) setInsights(data.insights || "");
                if (data.external_contexts) {
                    const enriched = data.external_contexts.map((ctx, idx)=>({
                            ...ctx,
                            id: ctx.url + '-' + idx,
                            favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,
                            isSelected: true
                        }));
                    setExternalContexts(enriched);
                }
            }
            setIsThinking(false);
            setThinking('');
        } else {
            console.log("[DEBUG] No message or thought in response:", data);
        }
        // Handle chart data if present (even if null)
        if ('chart' in data) {
            console.log("[DEBUG] Chart data received:", data.chart);
            setChartData(data.chart);
        }
        // Handle causality data if present (even if null)
        if ('causality' in data) {
            console.log("[DEBUG] Causality data received:", data.causality);
            setCausalityData(data.causality);
        }
        // Handle external contexts if present (even if null)
        if ('external_contexts' in data && data.external_contexts) {
            console.log("[DEBUG] External contexts received:", data.external_contexts);
            const enriched = data.external_contexts.map((ctx, idx)=>({
                    ...ctx,
                    id: ctx.url + '-' + idx,
                    favicon: `https://www.google.com/s2/favicons?domain=${new URL(ctx.url).hostname}`,
                    isSelected: true
                }));
            setExternalContexts(enriched);
        } else if ('external_contexts' in data && data.external_contexts === null) {
            // Clear external contexts if null
            setExternalContexts([]);
        }
        // Handle insights if present (even if null)
        if ('insights' in data) {
            console.log("[DEBUG] Insights received:", data.insights);
            setInsights(data.insights || "");
        }
    };
    // Connect to WebSocket with a specific slug
    const connectWithSlug = (slug)=>{
        console.log("[DEBUG] Connecting with slug:", slug);
        // Only update if the slug has changed
        if (currentSlug !== slug) {
            setCurrentSlug(slug);
            // Connect with the slug - our improved WebSocket client will handle
            // updating the slug without creating a new connection if possible
            __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["websocketClient"].connect(slug);
        } else {
            console.log("[DEBUG] Already connected with slug:", slug);
        }
    };
    // Send the message to the WebSocket server
    const sendMessage = (message)=>{
        // Add user message to the chat
        setMessages((prev)=>[
                ...prev,
                {
                    type: 'user',
                    content: message
                }
            ]);
        // Set thinking state
        setIsThinking(true);
        setThinking('Analyzing query...');
        // Create message payload
        const payload = {
            message
        };
        // Add file paths if available
        if (uploadedFiles.length > 0) {
            payload.files_path = uploadedFiles.map((file)=>file.path);
        }
        // Add model context with any available context information
        const modelContext = {};
        // Add chart context if available
        if (chartData || chartSelection?.point || chartSelection?.range) {
            modelContext.chart = {
                data: chartData
            };
            // Only add selection if it exists
            if (chartSelection) {
                modelContext.chart.selection = chartSelection;
            }
            console.log("[DEBUG] Adding chart context to message:", modelContext.chart);
        }
        // Only add model_context if we have context to share
        if (Object.keys(modelContext).length > 0) {
            payload.model_context = modelContext;
        }
        // Add selected external contexts if any
        if (selectedExternalContexts.length > 0) {
            payload.external_contexts = selectedExternalContexts;
        }
        // Send the message to the WebSocket server
        __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["websocketClient"].sendMessage(payload);
    };
    // Upload a file
    const handleFileUpload = async (file)=>{
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$websocket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["uploadFile"])(file);
            // Add to uploaded files
            if (result.filename) {
                setUploadedFiles((prev)=>[
                        ...prev,
                        {
                            name: file.name,
                            path: result.filename
                        }
                    ]);
            }
            return result.filename;
        } catch (error) {
            console.error('Error uploading file:', error);
            throw error;
        }
    };
    // Remove an uploaded file
    const removeUploadedFile = (fileName)=>{
        setUploadedFiles((prev)=>prev.filter((file)=>file.name !== fileName));
    };
    // Clear all uploaded files
    const clearUploadedFiles = ()=>{
        setUploadedFiles([]);
    };
    // Clear all messages
    const clearMessages = ()=>{
        setMessages([]);
        setThinking('');
        setIsThinking(false);
        setChartData(null);
    };
    // Create the context value
    const contextValue = {
        messages,
        thinking,
        isThinking,
        connectionStatus,
        chartData,
        causalityData,
        chartSelection,
        uploadedFiles,
        externalContexts,
        selectedExternalContexts,
        insights,
        currentSlug,
        sendMessage,
        uploadFile: handleFileUpload,
        removeUploadedFile,
        clearMessages,
        clearUploadedFiles,
        setChartSelection,
        setSelectedExternalContexts,
        connectWithSlug
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ChatContext.Provider, {
        value: contextValue,
        children: children
    }, void 0, false, {
        fileName: "[project]/app/contexts/ChatContext.tsx",
        lineNumber: 414,
        columnNumber: 5
    }, this);
}
function useChat() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ChatContext);
    if (context === undefined) {
        throw new Error('useChat must be used within a ChatProvider');
    }
    return context;
}
}}),
"[project]/app/layout.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$fonts$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/app/fonts.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__inter$3e$__ = __turbopack_context__.i("[next]/internal/font/google/inter_cb69f1e2.js [app-ssr] (ecmascript) <export default as inter>");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__inknutAntiqua$3e$__ = __turbopack_context__.i("[next]/internal/font/google/inknut_antiqua_8bb50917.js [app-ssr] (ecmascript) <export default as inknutAntiqua>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$BackgroundBlobs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/components/BackgroundBlobs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$ChatContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/contexts/ChatContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$ThemeContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/contexts/ThemeContext.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
function RootLayout({ children }) {
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["usePathname"])();
    const isOnChatPage = pathname?.startsWith('/chats');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_cb69f1e2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__inter$3e$__["inter"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inknut_antiqua_8bb50917$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__inknutAntiqua$3e$__["inknutAntiqua"].variable}`,
        suppressHydrationWarning: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("title", {
                        children: "Kairos App"
                    }, void 0, false, {
                        fileName: "[project]/app/layout.tsx",
                        lineNumber: 21,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                        name: "description",
                        content: "Kairos Application"
                    }, void 0, false, {
                        fileName: "[project]/app/layout.tsx",
                        lineNumber: 22,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/layout.tsx",
                lineNumber: 20,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$ThemeContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ThemeProvider"], {
                    children: [
                        !isOnChatPage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$components$2f$BackgroundBlobs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                            fileName: "[project]/app/layout.tsx",
                            lineNumber: 26,
                            columnNumber: 29
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$app$2f$contexts$2f$ChatContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ChatProvider"], {
                            children: children
                        }, void 0, false, {
                            fileName: "[project]/app/layout.tsx",
                            lineNumber: 27,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/layout.tsx",
                    lineNumber: 25,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/layout.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/layout.tsx",
        lineNumber: 19,
        columnNumber: 5
    }, this);
}
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__61f7a5e3._.js.map