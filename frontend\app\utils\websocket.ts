// WebSocket client utility for chat functionality

// Backend URL configuration - can be moved to environment variables later
export const BACKEND_WS_URL = process.env.NEXT_PUBLIC_BACKEND_WS_URL || 'ws://localhost:8000/ws';
export const BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';

// Message types
export interface ChatMessage {
  type: 'user' | 'ai';
  content: string;
}

export interface WebSocketMessage {
  message: string;
  slug?: string;
  files_path?: string[];
  external_contexts?: any[];
  model_context?: {
    chart?: {
      selection?: {
        point?: { name: string, value: number };
        range?: { start: string, end: string, values: any[] };
      };
      data?: any[];
    };
    // Can add other context types here in the future
    // document?: { ... };
    // code?: { ... };
    // etc.
  };
}

export interface WebSocketResponse {
  message?: string;
  thought?: string;
  chart?: any;
  status?: string;
  next_agent?: string;
  slug?: string;
  causality?: any;
  external_contexts?: any[];
  insights?: string;
}

// WebSocket connection states
export type ConnectionStatus = 'connecting' | 'connected' | 'disconnected' | 'error';

// WebSocket client class
export class WebSocketClient {
  private socket: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectTimeout: NodeJS.Timeout | null = null;
  private messageQueue: WebSocketMessage[] = [];
  private onMessageCallback: ((data: WebSocketResponse) => void) | null = null;
  private onStatusChangeCallback: ((status: ConnectionStatus) => void) | null = null;

  constructor() {
    // Initialize in a disconnected state
    this.notifyStatusChange('disconnected');
  }

  // Connect to the WebSocket server
  public connect(): void {
    if (this.socket?.readyState === WebSocket.OPEN) {
      console.log('WebSocket already connected');
      return;
    }

    try {
      this.notifyStatusChange('connecting');
      this.socket = new WebSocket(BACKEND_WS_URL);

      this.socket.onopen = this.handleOpen.bind(this);
      this.socket.onmessage = this.handleMessage.bind(this);
      this.socket.onclose = this.handleClose.bind(this);
      this.socket.onerror = this.handleError.bind(this);
    } catch (error) {
      console.error('Failed to connect to WebSocket:', error);
      this.notifyStatusChange('error');
      this.attemptReconnect();
    }
  }

  // Disconnect from the WebSocket server
  public disconnect(): void {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout);
      this.reconnectTimeout = null;
    }

    this.notifyStatusChange('disconnected');
  }

  // Send a message to the WebSocket server
  public sendMessage(message: WebSocketMessage): void {
    console.log("[DEBUG] Sending WebSocket message:", message);
    if (this.socket?.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.log('WebSocket not connected, queueing message');
      this.messageQueue.push(message);
      this.connect();
    }
  }

  // Set callback for incoming messages
  public onMessage(callback: (data: WebSocketResponse) => void): void {
    this.onMessageCallback = callback;
  }

  // Set callback for connection status changes
  public onStatusChange(callback: (status: ConnectionStatus) => void): void {
    this.onStatusChangeCallback = callback;
  }

  // Handle WebSocket open event
  private handleOpen(): void {
    console.log('WebSocket connected');
    this.reconnectAttempts = 0;
    this.notifyStatusChange('connected');

    // Send any queued messages
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      if (message) {
        this.sendMessage(message);
      }
    }
  }

  // Handle WebSocket message event
  private handleMessage(event: MessageEvent): void {
    try {
      console.log("[DEBUG] Raw WebSocket message received:", event.data);
      const data = JSON.parse(event.data) as WebSocketResponse;
      console.log("[DEBUG] Parsed WebSocket message:", data);
      if (this.onMessageCallback) {
        this.onMessageCallback(data);
      }
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }

  // Handle WebSocket close event
  private handleClose(event: CloseEvent): void {
    console.log(`WebSocket closed: ${event.code} ${event.reason}`);
    this.socket = null;
    this.notifyStatusChange('disconnected');
    this.attemptReconnect();
  }

  // Handle WebSocket error event
  private handleError(event: Event): void {
    console.error('WebSocket error:', event);
    this.notifyStatusChange('error');
    this.socket?.close();
  }

  // Attempt to reconnect to the WebSocket server
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('Max reconnect attempts reached');
      return;
    }

    const delay = Math.min(1000 * Math.pow(2, this.reconnectAttempts), 30000);
    console.log(`Attempting to reconnect in ${delay}ms`);

    this.reconnectTimeout = setTimeout(() => {
      this.reconnectAttempts++;
      this.connect();
    }, delay);
  }

  // Notify status change
  private notifyStatusChange(status: ConnectionStatus): void {
    if (this.onStatusChangeCallback) {
      this.onStatusChangeCallback(status);
    }
  }
}

// Create a singleton instance
export const websocketClient = new WebSocketClient();

// File upload utility function
export async function uploadFile(file: File): Promise<{ filename: string }> {
  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${BACKEND_API_URL}/upload`, {
    method: 'POST',
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`Upload failed: ${response.statusText}`);
  }

  return response.json();
}

