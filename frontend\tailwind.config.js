/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './app/**/*.{js,ts,jsx,tsx,mdx}',
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      /* ── fonts you already had ───────────────────────────── */
      fontFamily: {
        sans: ['var(--font-inter)'],
        inknut: ['var(--font-inknut-antiqua)'],
      },

      /* ── new utilities for the backdrop effect ───────────── */
      blur: {
        120: '120px',
        160: '160px'   // adding larger blur value
      },

      colors: {
        // Original Kairos colors
        kairosBlue:   '#3f6bfd',
        kairosYellow: '#fff68d',
        kairosGreen:  '#46fcb0',
        kairosBg:     '#0f0f10',

        // New Kairos color palette
        kairosRed:    '#f2e9e4',
        kairosGray1:  '#c9ada7',
        kairosGray2:  '#9a8c98',
        kairosGray3:  '#4a4e69',
        kairosGray4:  '#22223b',

        // Theme-aware colors using CSS variables
        background: 'var(--background)',
        foreground: 'var(--foreground)',
        primary: 'var(--primary)',
        secondary: 'var(--secondary)',
        accent: 'var(--accent)',
        muted: 'var(--muted)',
        border: 'var(--border)',
      },

      keyframes: {
        blob: {
          '0%,100%': { transform: 'translate(0,0) scale(1)' },
          '33%':     { transform: 'translate(40px,-60px) scale(1.05)' },
          '66%':     { transform: 'translate(-30px,40px) scale(.95)' },
        },
      },
      animation: {
        blob: 'blob 22s ease-in-out infinite',
      },
      textShadow: {
        'glow': '0 0 15px rgba(255, 255, 255, 0.5)',
        'subtle': '0 2px 4px rgba(0, 0, 0, 0.3)',
      },
    },
  },
  plugins: [require('@tailwindcss/typography'),
    function ({ addUtilities }) {
      const newUtilities = {
        '.text-shadow-glow': {
          textShadow: '0 0 15px rgba(255, 255, 255, 0.5)',
        },
        '.text-shadow-subtle': {
          textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)',
        },
      }
      addUtilities(newUtilities)
    }
  ],
};


