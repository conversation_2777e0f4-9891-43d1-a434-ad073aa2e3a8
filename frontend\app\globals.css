@import "tailwindcss";
@plugin '@tailwindcss/typography';

:root {
  /* Default light theme */
  --background: #ffffff;
  --foreground: #22223b;
  --primary: #f2e9e4;
  --secondary: #c9ada7;
  --accent: #9a8c98;
  --muted: #4a4e69;
  --border: #c9ada7;

  /* Legacy variables for compatibility */
  --foreground-rgb: 34, 34, 59;
  --background-rgb: 255, 255, 255;
}

.dark {
  /* Dark theme overrides */
  --background: #000000;
  --foreground: #ffffff;
  --primary: #f2e9e4;
  --secondary: #c9ada7;
  --accent: #9a8c98;
  --muted: #4a4e69;
  --border: #22223b;

  /* Legacy variables for compatibility */
  --foreground-rgb: 255, 255, 255;
  --background-rgb: 0, 0, 0;
}

body {
  color: var(--foreground);
  background: var(--background) !important;
  overflow-x: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Ensure background blobs container respects theme */
.bg-background {
  background-color: var(--background) !important;
}

.font-inknut {
  font-family: var(--font-inknut-antiqua);
}
/* Standard glass effect for main content area */
.glass-card {
  -webkit-backdrop-filter: blur(14px) saturate(180%);
  backdrop-filter: blur(14px) saturate(180%);
  border-radius: 1.5rem;
  transition: all 0.3s ease;
}

/* Dark mode glass card */
.dark .glass-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.08) inset,
    0 12px 24px -6px rgba(0, 0, 0, 0.5),
    0 0 20px rgba(63, 107, 253, 0.1);
}

/* Light mode glass card */
.glass-card {
  background: rgba(201, 173, 167, 0.1);
  border: 1px solid rgba(154, 140, 152, 0.2);
  box-shadow:
    0 0 0 1px rgba(154, 140, 152, 0.1) inset,
    0 12px 24px -6px rgba(34, 34, 59, 0.1),
    0 0 20px rgba(242, 233, 228, 0.2);
}

.glass-card:hover {
  background: rgba(255, 255, 255, 0.07);
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.07) inset,
    0 15px 30px -8px rgba(0, 0, 0, 0.5),
    0 0 25px rgba(63, 107, 253, 0.15);
}

/* New sidebar-specific glass effect */
.sidebar-glass-menu {
  background: rgba(0, 0, 0, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.15);

  -webkit-backdrop-filter: blur(20px) saturate(180%);
  backdrop-filter: blur(20px) saturate(180%);
  border-radius: 0.75rem;
  box-shadow:
    0 0 0 1px rgba(255, 255, 255, 0.05) inset,
    0 8px 16px rgba(0, 0, 0, 0.6);
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(255, 246, 141, 0.3);
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(172, 172, 169, 0.5);
}

/* For Firefox */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(150, 148, 150, 0.203) rgba(0, 0, 0, 0.1);
}

/* Grain overlay effect */
.grain-bg {
  background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
}

/* Vignette effect */
.vignette-effect {
  background: radial-gradient(circle at center, transparent 40%, rgba(0,0,0,0.4) 100%);
  mix-blend-mode: multiply;
}

/* Performance optimizations */
@media (max-width: 768px) {
  .vignette-effect {
    background: radial-gradient(circle at center, transparent 50%, rgba(0,0,0,0.3) 100%);
  }
}


