{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_e122dd21.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_e122dd21-module__lCCsDa__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_e122dd21.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22FontSwitcher.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22600%22,%22700%22]}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/montserrat_5f2a63b5.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"montserrat_5f2a63b5-module__619CUG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/montserrat_5f2a63b5.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22FontSwitcher.tsx%22,%22import%22:%22Montserrat%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22600%22,%22700%22]}],%22variableName%22:%22montserrat%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Montserrat', 'Montserrat Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_7992dd08.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"playfair_display_7992dd08-module__NTgqBW__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_7992dd08.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22FontSwitcher.tsx%22,%22import%22:%22Playfair_Display%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22700%22]}],%22variableName%22:%22playfair%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Playfair Display', 'Playfair Display Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/space_grotesk_254f4e67.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"space_grotesk_254f4e67-module__XUJ3wG__className\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 105, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/space_grotesk_254f4e67.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22FontSwitcher.tsx%22,%22import%22:%22Space_Grotesk%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22700%22]}],%22variableName%22:%22spacegrotesk%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Space Grotesk', 'Space Grotesk Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,6JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,6JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,6JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/components/FontSwitcher.tsx"], "sourcesContent": ["import React, { ReactNode } from 'react';\r\nimport { <PERSON><PERSON><PERSON>, Montserrat, Playfair_Display, Space_Grotesk } from 'next/font/google';\r\n\r\n// Call each font at the module level\r\nconst poppins = Poppins({ subsets: ['latin'], weight: ['400', '600', '700'] });\r\nconst montserrat = Montserrat({ subsets: ['latin'], weight: ['400', '600', '700'] });\r\nconst playfair = Playfair_Display({ subsets: ['latin'], weight: ['400', '700'] });\r\nconst spacegrotesk = Space_Grotesk({ subsets: ['latin'], weight: ['400', '700'] });\r\n\r\nconst fontMap = {\r\n  poppins,\r\n  montserrat,\r\n  playfair,\r\n  spacegrotesk,\r\n};\r\n\r\ntype FontKey = keyof typeof fontMap;\r\n\r\nexport default function FontSwitcher({\r\n  font = 'montserrat',\r\n  children,\r\n}: {\r\n  font?: FontKey;\r\n  children: ReactNode;\r\n}) {\r\n  const fontObj = fontMap[font] || fontMap.poppins;\r\n  return <div className={fontObj.className}>{children}</div>;\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;AASA,MAAM,UAAU;IACd,SAAA,2IAAA,CAAA,UAAO;IACP,YAAA,8IAAA,CAAA,UAAU;IACV,UAAA,oJAAA,CAAA,UAAQ;IACR,cAAA,iJAAA,CAAA,UAAY;AACd;AAIe,SAAS,aAAa,EACnC,OAAO,YAAY,EACnB,QAAQ,EAIT;IACC,MAAM,UAAU,OAAO,CAAC,KAAK,IAAI,QAAQ,OAAO;IAChD,qBAAO,8OAAC;QAAI,WAAW,QAAQ,SAAS;kBAAG;;;;;;AAC7C", "debugId": null}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'motion/react';\nimport { SunIcon, MoonIcon } from '@heroicons/react/24/outline';\nimport { useTheme } from '../contexts/ThemeContext';\n\ninterface ThemeToggleProps {\n  className?: string;\n}\n\nexport default function ThemeToggle({ className = '' }: ThemeToggleProps) {\n  const { theme, toggleTheme } = useTheme();\n\n  return (\n    <motion.button\n      onClick={toggleTheme}\n      className={`relative flex items-center justify-center w-12 h-6 rounded-full transition-all duration-300 ${\n        theme === 'dark' \n          ? 'bg-kairosGray3' \n          : 'bg-kairosGray1'\n      } ${className}`}\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {/* Toggle circle */}\n      <motion.div\n        className={`absolute w-5 h-5 rounded-full flex items-center justify-center ${\n          theme === 'dark' \n            ? 'bg-kairosRed' \n            : 'bg-white'\n        }`}\n        animate={{\n          x: theme === 'dark' ? 24 : 0,\n        }}\n        transition={{\n          type: 'spring',\n          stiffness: 500,\n          damping: 30,\n        }}\n      >\n        {/* Icon */}\n        <motion.div\n          initial={false}\n          animate={{\n            scale: 1,\n            rotate: theme === 'dark' ? 0 : 180,\n          }}\n          transition={{\n            duration: 0.3,\n          }}\n        >\n          {theme === 'dark' ? (\n            <MoonIcon className=\"w-3 h-3 text-kairosGray4\" />\n          ) : (\n            <SunIcon className=\"w-3 h-3 text-kairosGray3\" />\n          )}\n        </motion.div>\n      </motion.div>\n      \n      {/* Background icons */}\n      <div className=\"absolute inset-0 flex items-center justify-between px-1\">\n        <SunIcon className={`w-3 h-3 transition-opacity duration-300 ${\n          theme === 'light' ? 'opacity-0' : 'opacity-50'\n        } ${theme === 'dark' ? 'text-kairosRed' : 'text-kairosGray3'}`} />\n        <MoonIcon className={`w-3 h-3 transition-opacity duration-300 ${\n          theme === 'dark' ? 'opacity-0' : 'opacity-50'\n        } ${theme === 'dark' ? 'text-kairosRed' : 'text-kairosGray3'}`} />\n      </div>\n    </motion.button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AALA;;;;;AAWe,SAAS,YAAY,EAAE,YAAY,EAAE,EAAoB;IACtE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IAEtC,qBACE,8OAAC,kNAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAW,CAAC,4FAA4F,EACtG,UAAU,SACN,mBACA,iBACL,CAAC,EAAE,WAAW;QACf,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY,CAAC,UAAU,EAAE,UAAU,UAAU,SAAS,QAAQ,KAAK,CAAC;;0BAGpE,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,+DAA+D,EACzE,UAAU,SACN,iBACA,YACJ;gBACF,SAAS;oBACP,GAAG,UAAU,SAAS,KAAK;gBAC7B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;0BAGA,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;oBACT,SAAS;wBACP,OAAO;wBACP,QAAQ,UAAU,SAAS,IAAI;oBACjC;oBACA,YAAY;wBACV,UAAU;oBACZ;8BAEC,UAAU,uBACT,8OAAC,+MAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;6CAEpB,8OAAC,6MAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAMzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,6MAAA,CAAA,UAAO;wBAAC,WAAW,CAAC,wCAAwC,EAC3D,UAAU,UAAU,cAAc,aACnC,CAAC,EAAE,UAAU,SAAS,mBAAmB,oBAAoB;;;;;;kCAC9D,8OAAC,+MAAA,CAAA,WAAQ;wBAAC,WAAW,CAAC,wCAAwC,EAC5D,UAAU,SAAS,cAAc,aAClC,CAAC,EAAE,UAAU,SAAS,mBAAmB,oBAAoB;;;;;;;;;;;;;;;;;;AAItE", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/landing/ModernNavbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport Link from 'next/link';\r\nimport { usePathname } from 'next/navigation';\r\nimport { motion } from 'motion/react';\r\nimport ThemeToggle from '../ThemeToggle';\r\nimport { useTheme } from '../../contexts/ThemeContext';\r\n\r\nexport default function ModernNavbar() {\r\n  const pathname = usePathname();\r\n  const { theme } = useTheme();\r\n  const [scrolled, setScrolled] = useState(false);\r\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);\r\n\r\n  // Handle scroll effect for navbar\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const offset = window.scrollY;\r\n      if (offset > 50) {\r\n        setScrolled(true);\r\n      } else {\r\n        setScrolled(false);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n\r\n    // Enable smooth scrolling for anchor links\r\n    const originalScrollBehavior = document.documentElement.style.scrollBehavior;\r\n    document.documentElement.style.scrollBehavior = 'smooth';\r\n\r\n    return () => {\r\n      window.removeEventListener('scroll', handleScroll);\r\n      document.documentElement.style.scrollBehavior = originalScrollBehavior;\r\n    };\r\n  }, []);\r\n\r\n  const isActive = (path: string) => {\r\n    return pathname === path;\r\n  };\r\n\r\n  // Helper for smooth scroll with offset\r\n  const handleSmoothScroll = (e: React.MouseEvent<HTMLAnchorElement>, id: string) => {\r\n    e.preventDefault();\r\n    if (id === 'top') {\r\n      window.scrollTo({ top: 0, behavior: 'smooth' });\r\n      return;\r\n    }\r\n    const el = document.getElementById(id);\r\n    if (el) {\r\n      const yOffset = -100; // Adjust this value to match your navbar height\r\n      const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;\r\n      window.scrollTo({ top: y, behavior: 'smooth' });\r\n    }\r\n  };\r\n\r\n  return (\r\n    <motion.nav\r\n      initial={{ opacity: 0, y: -20 }}\r\n      animate={{ opacity: 1, y: 0 }}\r\n      transition={{ duration: 0.5 }}\r\n      className={`fixed max-w-7xl left-1/2 -translate-x-1/2 z-60 transition-all duration-300 ${\r\n        scrolled\r\n          ? 'py-3 mx-auto my-3 bg-background/80 backdrop-blur-lg rounded-full border border-border/20'\r\n          : 'py-5 mx-auto bg-transparent'\r\n      }`}\r\n    >\r\n      <div className=\"max-w-7xl mx-auto px-2 sm:px-3 lg:px-4\">\r\n        <div className=\"flex justify-between items-center\">\r\n          {/* Logo */}\r\n          <Link href=\"/\" className=\"flex items-center\">\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.5, delay: 0.2 }}\r\n              className=\"flex items-center\"\r\n            >\r\n              <span className=\"text-2xl font-bold text-foreground font-inknut\">\r\n                Kairos\r\n              </span>\r\n              <span className=\"ml-1 text-kairosGreen text-2xl\">AI</span>\r\n            </motion.div>\r\n          </Link>\r\n\r\n          {/* Desktop Navigation */}\r\n          <div className=\"hidden md:flex items-center\">\r\n            <motion.div\r\n              initial={{ opacity: 0, y: -10 }}\r\n              animate={{ opacity: 1, y: 0 }}\r\n              transition={{ duration: 0.5, delay: 0.3 }}\r\n              className=\"flex space-x-6 ml-12\"\r\n            >\r\n              {[\r\n                { name: 'About', path: '#about' },\r\n                { name: 'Features', path: '#features' },\r\n                { name: 'Founders', path: '#founders' },\r\n              ].map((item, index) => (\r\n                <a\r\n                  key={item.path}\r\n                  href={item.path}\r\n                  onClick={item.path.startsWith('#') ? (e) => handleSmoothScroll(e, item.path.substring(1)) : undefined}\r\n                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-300 ${\r\n                    isActive(item.path)\r\n                      ? 'text-foreground bg-primary'\r\n                      : 'text-muted hover:text-foreground hover:bg-secondary/20'\r\n                  }`}\r\n                >\r\n                  {item.name}\r\n                </a>\r\n              ))}\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ opacity: 0, scale: 0.8 }}\r\n              animate={{ opacity: 1, scale: 1 }}\r\n              transition={{ duration: 0.5, delay: 0.4 }}\r\n              className=\"ml-4 flex items-center space-x-3\"\r\n            >\r\n              <ThemeToggle />\r\n              <a\r\n                href=\"#top\"\r\n                onClick={(e) => handleSmoothScroll(e, 'top')}\r\n                className=\"px-4 py-2 bg-primary text-foreground rounded-full hover:bg-secondary transition-all duration-300 shadow-lg font-medium cursor-pointer\"\r\n              >\r\n                Get Started\r\n              </a>\r\n            </motion.div>\r\n          </div>\r\n\r\n          {/* Mobile menu button */}\r\n          <div className=\"md:hidden flex items-center\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\r\n              className=\"inline-flex items-center justify-center p-2 rounded-full text-gray-300 hover:text-white focus:outline-none bg-black/50 backdrop-blur-sm border border-white/10\"\r\n              aria-expanded=\"false\"\r\n            >\r\n              <span className=\"sr-only\">Open main menu</span>\r\n              {/* Icon when menu is closed */}\r\n              <svg\r\n                className={`${mobileMenuOpen ? 'hidden' : 'block'} h-6 w-6`}\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n                aria-hidden=\"true\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth=\"2\"\r\n                  d=\"M4 6h16M4 12h16M4 18h16\"\r\n                />\r\n              </svg>\r\n              {/* Icon when menu is open */}\r\n              <svg\r\n                className={`${mobileMenuOpen ? 'block' : 'hidden'} h-6 w-6`}\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                fill=\"none\"\r\n                viewBox=\"0 0 24 24\"\r\n                stroke=\"currentColor\"\r\n                aria-hidden=\"true\"\r\n              >\r\n                <path\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                  strokeWidth=\"2\"\r\n                  d=\"M6 18L18 6M6 6l12 12\"\r\n                />\r\n              </svg>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mobile menu, show/hide based on menu state */}\r\n      <motion.div\r\n        initial={{ opacity: 0, height: 0 }}\r\n        animate={{\r\n          opacity: mobileMenuOpen ? 1 : 0,\r\n          height: mobileMenuOpen ? 'auto' : 0,\r\n        }}\r\n        transition={{ duration: 0.3 }}\r\n        className={`md:hidden overflow-visible flex justify-center items-start w-full fixed left-0 top-20 z-50`}\r\n        style={{ pointerEvents: mobileMenuOpen ? 'auto' : 'none' }}\r\n      >\r\n        <div className=\"relative  w-72 max-w-[90vw] bg-black/90 backdrop-blur-lg rounded-3xl border border-white/10 shadow-2xl flex flex-col items-center px-4 py-6\">\r\n          {/* Removed close button from inside the menu */}\r\n          <div className=\"flex flex-col space-y-3 w-full mt-6\">\r\n            {[\r\n              { name: 'About', path: '#about' },\r\n              { name: 'Features', path: '#features' },\r\n              { name: 'Founders', path: '#founders' },\r\n            ].map((item) => (\r\n              <a\r\n                key={item.path}\r\n                href={item.path}\r\n                onClick={item.path.startsWith('#') ? (e) => { handleSmoothScroll(e, item.path.substring(1)); setMobileMenuOpen(false); } : undefined}\r\n                className={`block px-3 py-2 rounded-md text-base font-medium transition-all duration-200 ${\r\n                  isActive(item.path)\r\n                    ? 'text-black bg-kairosYellow'\r\n                    : 'text-gray-300 hover:text-white hover:bg-white/10'\r\n                }`}\r\n              >\r\n                {item.name}\r\n              </a>\r\n            ))}\r\n            <a\r\n              href=\"#top\"\r\n              onClick={(e) => { handleSmoothScroll(e, 'top'); setMobileMenuOpen(false); }}\r\n              className=\"block w-full mt-4 px-3 py-2 bg-kairosBlue text-white rounded-full hover:bg-kairosBlue/90 transition-colors text-center font-medium shadow-lg shadow-kairosBlue/20 cursor-pointer\"\r\n            >\r\n              Get Started\r\n            </a>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </motion.nav>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,SAAS,OAAO,OAAO;YAC7B,IAAI,SAAS,IAAI;gBACf,YAAY;YACd,OAAO;gBACL,YAAY;YACd;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,2CAA2C;QAC3C,MAAM,yBAAyB,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc;QAC5E,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAEhD,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,SAAS,eAAe,CAAC,KAAK,CAAC,cAAc,GAAG;QAClD;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,CAAC;QAChB,OAAO,aAAa;IACtB;IAEA,uCAAuC;IACvC,MAAM,qBAAqB,CAAC,GAAwC;QAClE,EAAE,cAAc;QAChB,IAAI,OAAO,OAAO;YAChB,OAAO,QAAQ,CAAC;gBAAE,KAAK;gBAAG,UAAU;YAAS;YAC7C;QACF;QACA,MAAM,KAAK,SAAS,cAAc,CAAC;QACnC,IAAI,IAAI;YACN,MAAM,UAAU,CAAC,KAAK,gDAAgD;YACtE,MAAM,IAAI,GAAG,qBAAqB,GAAG,GAAG,GAAG,OAAO,WAAW,GAAG;YAChE,OAAO,QAAQ,CAAC;gBAAE,KAAK;gBAAG,UAAU;YAAS;QAC/C;IACF;IAEA,qBACE,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;QAC5B,WAAW,CAAC,2EAA2E,EACrF,WACI,6FACA,+BACJ;;0BAEF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCACvB,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAI;gCAClC,SAAS;oCAAE,SAAS;oCAAG,OAAO;gCAAE;gCAChC,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;gCACxC,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDAAiD;;;;;;kDAGjE,8OAAC;wCAAK,WAAU;kDAAiC;;;;;;;;;;;;;;;;;sCAKrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;8CAET;wCACC;4CAAE,MAAM;4CAAS,MAAM;wCAAS;wCAChC;4CAAE,MAAM;4CAAY,MAAM;wCAAY;wCACtC;4CAAE,MAAM;4CAAY,MAAM;wCAAY;qCACvC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC;4CAEC,MAAM,KAAK,IAAI;4CACf,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAM,mBAAmB,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,MAAM;4CAC5F,WAAW,CAAC,uEAAuE,EACjF,SAAS,KAAK,IAAI,IACd,+BACA,0DACJ;sDAED,KAAK,IAAI;2CATL,KAAK,IAAI;;;;;;;;;;8CAcpB,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,WAAU;;sDAEV,8OAAC,iIAAA,CAAA,UAAW;;;;;sDACZ,8OAAC;4CACC,MAAK;4CACL,SAAS,CAAC,IAAM,mBAAmB,GAAG;4CACtC,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,kBAAkB,CAAC;gCAClC,WAAU;gCACV,iBAAc;;kDAEd,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAE1B,8OAAC;wCACC,WAAW,GAAG,iBAAiB,WAAW,QAAQ,QAAQ,CAAC;wCAC3D,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;kDAIN,8OAAC;wCACC,WAAW,GAAG,iBAAiB,UAAU,SAAS,QAAQ,CAAC;wCAC3D,OAAM;wCACN,MAAK;wCACL,SAAQ;wCACR,QAAO;wCACP,eAAY;kDAEZ,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAY;4CACZ,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASd,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,QAAQ;gBAAE;gBACjC,SAAS;oBACP,SAAS,iBAAiB,IAAI;oBAC9B,QAAQ,iBAAiB,SAAS;gBACpC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAW,CAAC,0FAA0F,CAAC;gBACvG,OAAO;oBAAE,eAAe,iBAAiB,SAAS;gBAAO;0BAEzD,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;4BACZ;gCACC;oCAAE,MAAM;oCAAS,MAAM;gCAAS;gCAChC;oCAAE,MAAM;oCAAY,MAAM;gCAAY;gCACtC;oCAAE,MAAM;oCAAY,MAAM;gCAAY;6BACvC,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;oCAEC,MAAM,KAAK,IAAI;oCACf,SAAS,KAAK,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;wCAAQ,mBAAmB,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC;wCAAK,kBAAkB;oCAAQ,IAAI;oCAC3H,WAAW,CAAC,6EAA6E,EACvF,SAAS,KAAK,IAAI,IACd,+BACA,oDACJ;8CAED,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAYlB,8OAAC;gCACC,MAAK;gCACL,SAAS,CAAC;oCAAQ,mBAAmB,GAAG;oCAAQ,kBAAkB;gCAAQ;gCAC1E,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 654, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/LandingBackgroundBlobs.tsx"], "sourcesContent": ["// components/LandingBackgroundBlobs.tsx\r\n'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { motion } from 'motion/react';\r\n\r\ninterface XYBlobProps {\r\n  x: string;           // X position from left (e.g., \"10%\", \"200px\")\r\n  y: string;           // Y position from top (e.g., \"20%\", \"300px\")\r\n  size: string;        // Width/height (e.g., \"40vw\", \"300px\")\r\n  color: string;       // Main color (e.g., \"#3f6bfd\")\r\n  opacity?: number;    // 0-1 opacity value\r\n  blur?: string;       // Blur amount (e.g., \"80px\")\r\n  mirror?: boolean;    // Whether to add mirror effect\r\n  animate?: boolean;   // Whether to animate the blob\r\n  delay?: number;      // Animation delay\r\n  duration?: number;   // Animation duration\r\n  floatAmount?: number; // How much the blob floats (px)\r\n}\r\n\r\n// XYBlob component for positioning blobs anywhere\r\nfunction XYBlob({\r\n  x,\r\n  y,\r\n  size,\r\n  color,\r\n  opacity = 0.7,\r\n  blur = \"80px\",\r\n  mirror = false,\r\n  animate = false,\r\n  delay = 0,\r\n  duration = 20,\r\n  floatAmount = 30\r\n}: XYBlobProps) {\r\n  // Create gradient based on color\r\n  const getGradient = (c: string) => {\r\n    const rgba = hexToRgba(c, 0.4);\r\n    return `radial-gradient(circle at 50% 50%, ${c} 0%, ${rgba} 30%, transparent 70%)`;\r\n  };\r\n\r\n  // Helper to convert hex to rgba\r\n  const hexToRgba = (hex: string, alpha: number) => {\r\n    const r = parseInt(hex.slice(1, 3), 16);\r\n    const g = parseInt(hex.slice(3, 5), 16);\r\n    const b = parseInt(hex.slice(5, 7), 16);\r\n    return `rgba(${r},${g},${b},${alpha})`;\r\n  };\r\n\r\n  // Generate the background gradient\r\n  const backgroundGradient = getGradient(color);\r\n\r\n  // Simplified animation variants\r\n  const blobVariants = {\r\n    initial: {\r\n      scale: 0.9,\r\n      opacity: 0\r\n    },\r\n    animate: {\r\n      scale: 1,\r\n      opacity: opacity,\r\n      transition: {\r\n        duration: 0.8,\r\n        delay: delay,\r\n        ease: \"easeOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  // Separate pulse variant for animated blobs\r\n  const pulseVariants = animate ? {\r\n    scale: [1, 1.03, 0.97, 1],\r\n    x: [0, floatAmount/2, -floatAmount/2, 0],\r\n    y: [0, -floatAmount/2, floatAmount/2, 0],\r\n    transition: {\r\n      duration: duration * 1.5,\r\n      repeat: Infinity,\r\n      repeatType: \"reverse\" as const,\r\n      ease: \"easeInOut\"\r\n    }\r\n  } : undefined;\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"pointer-events-none fixed -z-40 rounded-full mix-blend-screen\"\r\n      initial=\"initial\"\r\n      animate={animate ? \"pulse\" : \"animate\"}\r\n      variants={blobVariants}\r\n      {...(animate && { animate: pulseVariants })}\r\n      style={{\r\n        left: x,\r\n        top: y,\r\n        width: size,\r\n        height: size,\r\n        filter: `blur(${blur})`,\r\n        background: backgroundGradient\r\n      }}\r\n    >\r\n      {mirror && (\r\n        <div\r\n          className=\"absolute inset-0 scale-x-[-1]\"\r\n          style={{ background: backgroundGradient }}\r\n        />\r\n      )}\r\n    </motion.div>\r\n  );\r\n}\r\n\r\nexport default function LandingBackgroundBlobs() {\r\n  // State to track if we should animate (disable on mobile for performance)\r\n  const [shouldAnimate, setShouldAnimate] = useState(false);\r\n\r\n  // Check if we're on a device that can handle animations\r\n  useEffect(() => {\r\n    // Only enable animations on desktop devices\r\n    const checkDevice = () => {\r\n      const isMobile = window.innerWidth < 768;\r\n      setShouldAnimate(!isMobile);\r\n    };\r\n\r\n    // Check on mount\r\n    checkDevice();\r\n\r\n    // Check on resize\r\n    window.addEventListener('resize', checkDevice);\r\n    return () => window.removeEventListener('resize', checkDevice);\r\n  }, []);\r\n\r\n  return (\r\n    <>\r\n      {/* dark base */}\r\n      <div className=\"fixed inset-0 -z-50 bg-black overflow-hidden\" />\r\n\r\n      {/* Main blue blob in top left */}\r\n      <XYBlob\r\n        x=\"-10%\"\r\n        y=\"-5%\"\r\n        size=\"45vw\"\r\n        color=\"#3f6bfd\"\r\n        opacity={0.6}\r\n        blur=\"120px\"\r\n        animate={shouldAnimate}\r\n        delay={0}\r\n        duration={8}\r\n        floatAmount={40}\r\n      />\r\n\r\n      {/* Yellow blob in top right */}\r\n      <XYBlob\r\n        x=\"70%\"\r\n        y=\"-10%\"\r\n        size=\"40vw\"\r\n        color=\"#fff68d\"\r\n        opacity={0.4}\r\n        blur=\"100px\"\r\n        mirror={true}\r\n        animate={shouldAnimate}\r\n        delay={0.2}\r\n        duration={9}\r\n        floatAmount={35}\r\n      />\r\n\r\n      {/* Green blob in middle */}\r\n      <XYBlob\r\n        x=\"30%\"\r\n        y=\"40%\"\r\n        size=\"50vw\"\r\n        color=\"#46fcb0\"\r\n        opacity={0.3}\r\n        blur=\"90px\"\r\n        animate={shouldAnimate}\r\n        delay={0.4}\r\n        duration={10}\r\n        floatAmount={50}\r\n      />\r\n\r\n      {/* Blue blob in bottom right */}\r\n      <XYBlob\r\n        x=\"60%\"\r\n        y=\"70%\"\r\n        size=\"45vw\"\r\n        color=\"#3f6bfd\"\r\n        opacity={0.5}\r\n        blur=\"110px\"\r\n        animate={shouldAnimate}\r\n        delay={0.6}\r\n        duration={9}\r\n        floatAmount={45}\r\n      />\r\n    </>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;AAGxC;AACA;AAHA;;;;AAmBA,kDAAkD;AAClD,SAAS,OAAO,EACd,CAAC,EACD,CAAC,EACD,IAAI,EACJ,KAAK,EACL,UAAU,GAAG,EACb,OAAO,MAAM,EACb,SAAS,KAAK,EACd,UAAU,KAAK,EACf,QAAQ,CAAC,EACT,WAAW,EAAE,EACb,cAAc,EAAE,EACJ;IACZ,iCAAiC;IACjC,MAAM,cAAc,CAAC;QACnB,MAAM,OAAO,UAAU,GAAG;QAC1B,OAAO,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,KAAK,sBAAsB,CAAC;IACpF;IAEA,gCAAgC;IAChC,MAAM,YAAY,CAAC,KAAa;QAC9B,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,MAAM,IAAI,SAAS,IAAI,KAAK,CAAC,GAAG,IAAI;QACpC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC;IACxC;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,YAAY;IAEvC,gCAAgC;IAChC,MAAM,eAAe;QACnB,SAAS;YACP,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,OAAO;YACP,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,OAAO;gBACP,MAAM;YACR;QACF;IACF;IAEA,4CAA4C;IAC5C,MAAM,gBAAgB,UAAU;QAC9B,OAAO;YAAC;YAAG;YAAM;YAAM;SAAE;QACzB,GAAG;YAAC;YAAG,cAAY;YAAG,CAAC,cAAY;YAAG;SAAE;QACxC,GAAG;YAAC;YAAG,CAAC,cAAY;YAAG,cAAY;YAAG;SAAE;QACxC,YAAY;YACV,UAAU,WAAW;YACrB,QAAQ;YACR,YAAY;YACZ,MAAM;QACR;IACF,IAAI;IAEJ,qBACE,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAQ;QACR,SAAS,UAAU,UAAU;QAC7B,UAAU;QACT,GAAI,WAAW;YAAE,SAAS;QAAc,CAAC;QAC1C,OAAO;YACL,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;YACR,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACvB,YAAY;QACd;kBAEC,wBACC,8OAAC;YACC,WAAU;YACV,OAAO;gBAAE,YAAY;YAAmB;;;;;;;;;;;AAKlD;AAEe,SAAS;IACtB,0EAA0E;IAC1E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,wDAAwD;IACxD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,4CAA4C;QAC5C,MAAM,cAAc;YAClB,MAAM,WAAW,OAAO,UAAU,GAAG;YACrC,iBAAiB,CAAC;QACpB;QAEA,iBAAiB;QACjB;QAEA,kBAAkB;QAClB,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;0BAIf,8OAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,QAAQ;gBACR,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;0BAIf,8OAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;0BAIf,8OAAC;gBACC,GAAE;gBACF,GAAE;gBACF,MAAK;gBACL,OAAM;gBACN,SAAS;gBACT,MAAK;gBACL,SAAS;gBACT,OAAO;gBACP,UAAU;gBACV,aAAa;;;;;;;;AAIrB", "debugId": null}}, {"offset": {"line": 855, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/landing/DemoChart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';\r\nimport { motion } from 'motion/react';\r\n\r\n// Sample data for the chart\r\nconst initialData = [\r\n  { name: 'Jan', actual: 4000, predicted: 4200 },\r\n  { name: 'Feb', actual: 3000, predicted: 3100 },\r\n  { name: 'Mar', actual: 2000, predicted: 2300 },\r\n  { name: 'Apr', actual: 2780, predicted: 2600 },\r\n  { name: 'May', actual: 1890, predicted: 1800 },\r\n  { name: 'Jun', actual: 2390, predicted: 2500 },\r\n  { name: 'Jul', actual: 3490, predicted: 3400 },\r\n  { name: 'Aug', actual: 4000, predicted: 4100 },\r\n  { name: 'Sep', actual: 5000, predicted: 4800 },\r\n  { name: 'Oct', actual: 6000, predicted: 5900 },\r\n  { name: 'Nov', actual: 7000, predicted: 7200 },\r\n  { name: 'Dec', actual: 8000, predicted: 8100 },\r\n];\r\n\r\n// Custom tooltip component\r\nconst CustomTooltip = ({ active, payload, label }: any) => {\r\n  if (!active || !payload?.length) return null;\r\n\r\n  return (\r\n    <div className=\"bg-black/80 p-2 rounded border border-white/20 text-white text-xs shadow-lg\">\r\n      <p className=\"font-bold\">{label}</p>\r\n      <p className=\"text-kairosGreen\">Actual: {payload[0].value}</p>\r\n      <p className=\"text-red-400\">Predicted: {payload[1].value}</p>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default function DemoChart() {\r\n  const [data, setData] = useState(initialData);\r\n  const [key, setKey] = useState(0);\r\n\r\n  // Effect to periodically refresh the animation\r\n  useEffect(() => {\r\n    const interval = setInterval(() => {\r\n      // Add small random variations to make the chart look dynamic\r\n      const newData = initialData.map(item => ({\r\n        ...item,\r\n        actual: item.actual + (Math.random() * 400 - 200),\r\n        predicted: item.predicted + (Math.random() * 400 - 200)\r\n      }));\r\n      setData(newData);\r\n      setKey(prev => prev + 1);\r\n    }, 6000); // Refresh every 6 seconds\r\n\r\n    return () => clearInterval(interval);\r\n  }, []);\r\n\r\n  return (\r\n    <motion.div\r\n      className=\"w-full h-full min-h-[300px] p-4 bg-transparent\"\r\n      initial={{ opacity: 0 }}\r\n      whileInView={{ opacity: 1 }}\r\n      viewport={{ once: true, amount: 0.3 }}\r\n      transition={{ duration: 0.7 }}\r\n    >\r\n      <motion.div\r\n        className=\"mb-4\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        whileInView={{ opacity: 1, y: 0 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.5, delay: 0.2 }}\r\n      >\r\n        <h3 className=\"text-lg font-inknut text-red-400\">Time Series Forecast Demo</h3>\r\n        <p className=\"text-sm text-white\">Actual vs Predicted Values</p>\r\n      </motion.div>\r\n\r\n      <ResponsiveContainer width=\"100%\" height={300}>\r\n        <LineChart\r\n          key={key}\r\n          data={data}\r\n          margin={{\r\n            top: 5,\r\n            right: 30,\r\n            left: 20,\r\n            bottom: 5,\r\n          }}\r\n        >\r\n          <CartesianGrid strokeDasharray=\"3 3\" stroke=\"rgba(255,255,255,0.05)\" />\r\n          <XAxis dataKey=\"name\" stroke=\"rgba(255,255,255,0.7)\" />\r\n          <YAxis stroke=\"rgba(255,255,255,0.7)\" />\r\n          <Tooltip content={<CustomTooltip />} />\r\n          <Line\r\n            type=\"monotone\"\r\n            dataKey=\"actual\"\r\n            stroke=\"#46fcb0\"\r\n            strokeWidth={2}\r\n            dot={{ r: 4, strokeWidth: 2, fill: \"#46fcb0\", stroke: \"#46fcb0\" }}\r\n            activeDot={{ r: 6, fill: \"#46fcb0\", stroke: \"#fff\" }}\r\n            isAnimationActive={true}\r\n            animationDuration={4000}\r\n            animationEasing=\"ease-in-out\"\r\n          />\r\n          <Line\r\n            type=\"monotone\"\r\n            dataKey=\"predicted\"\r\n            stroke=\"#ff6b6b\"\r\n            strokeWidth={2}\r\n            dot={{ r: 4, strokeWidth: 2, fill: \"#ff6b6b\", stroke: \"#ff6b6b\" }}\r\n            activeDot={{ r: 6, fill: \"#ff6b6b\", stroke: \"#fff\" }}\r\n            isAnimationActive={true}\r\n            animationDuration={2000}\r\n            animationEasing=\"ease-in-out\"\r\n          />\r\n        </LineChart>\r\n      </ResponsiveContainer>\r\n\r\n      <motion.div\r\n        className=\"flex justify-center mt-4 space-x-6\"\r\n        initial={{ opacity: 0, y: 20 }}\r\n        whileInView={{ opacity: 1, y: 0 }}\r\n        viewport={{ once: true }}\r\n        transition={{ duration: 0.5, delay: 0.8 }}\r\n      >\r\n        <div className=\"flex items-center\">\r\n          <div className=\"w-3 h-3 rounded-full bg-kairosGreen mr-2\"></div>\r\n          <span className=\"text-sm text-white\">Actual</span>\r\n        </div>\r\n        <div className=\"flex items-center\">\r\n          <div className=\"w-3 h-3 rounded-full bg-red-400 mr-2\"></div>\r\n          <span className=\"text-sm text-white\">Predicted</span>\r\n        </div>\r\n      </motion.div>\r\n    </motion.div>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,4BAA4B;AAC5B,MAAM,cAAc;IAClB;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;IAC7C;QAAE,MAAM;QAAO,QAAQ;QAAM,WAAW;IAAK;CAC9C;AAED,2BAA2B;AAC3B,MAAM,gBAAgB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAO;IACpD,IAAI,CAAC,UAAU,CAAC,SAAS,QAAQ,OAAO;IAExC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAE,WAAU;0BAAa;;;;;;0BAC1B,8OAAC;gBAAE,WAAU;;oBAAmB;oBAAS,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;0BACzD,8OAAC;gBAAE,WAAU;;oBAAe;oBAAY,OAAO,CAAC,EAAE,CAAC,KAAK;;;;;;;;;;;;;AAG9D;AAEe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/B,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,6DAA6D;YAC7D,MAAM,UAAU,YAAY,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACvC,GAAG,IAAI;oBACP,QAAQ,KAAK,MAAM,GAAG,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;oBAChD,WAAW,KAAK,SAAS,GAAG,CAAC,KAAK,MAAM,KAAK,MAAM,GAAG;gBACxD,CAAC;YACD,QAAQ;YACR,OAAO,CAAA,OAAQ,OAAO;QACxB,GAAG,OAAO,0BAA0B;QAEpC,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBACE,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;QAAE;QACtB,aAAa;YAAE,SAAS;QAAE;QAC1B,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAI;QACpC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,8OAAC;wBAAG,WAAU;kCAAmC;;;;;;kCACjD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;0BAGpC,8OAAC,mKAAA,CAAA,sBAAmB;gBAAC,OAAM;gBAAO,QAAQ;0BACxC,cAAA,8OAAC,qJAAA,CAAA,YAAS;oBAER,MAAM;oBACN,QAAQ;wBACN,KAAK;wBACL,OAAO;wBACP,MAAM;wBACN,QAAQ;oBACV;;sCAEA,8OAAC,6JAAA,CAAA,gBAAa;4BAAC,iBAAgB;4BAAM,QAAO;;;;;;sCAC5C,8OAAC,qJAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAO,QAAO;;;;;;sCAC7B,8OAAC,qJAAA,CAAA,QAAK;4BAAC,QAAO;;;;;;sCACd,8OAAC,uJAAA,CAAA,UAAO;4BAAC,uBAAS,8OAAC;;;;;;;;;;sCACnB,8OAAC,oJAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,QAAO;4BACP,aAAa;4BACb,KAAK;gCAAE,GAAG;gCAAG,aAAa;gCAAG,MAAM;gCAAW,QAAQ;4BAAU;4BAChE,WAAW;gCAAE,GAAG;gCAAG,MAAM;gCAAW,QAAQ;4BAAO;4BACnD,mBAAmB;4BACnB,mBAAmB;4BACnB,iBAAgB;;;;;;sCAElB,8OAAC,oJAAA,CAAA,OAAI;4BACH,MAAK;4BACL,SAAQ;4BACR,QAAO;4BACP,aAAa;4BACb,KAAK;gCAAE,GAAG;gCAAG,aAAa;gCAAG,MAAM;gCAAW,QAAQ;4BAAU;4BAChE,WAAW;gCAAE,GAAG;gCAAG,MAAM;gCAAW,QAAQ;4BAAO;4BACnD,mBAAmB;4BACnB,mBAAmB;4BACnB,iBAAgB;;;;;;;mBAjCb;;;;;;;;;;0BAsCT,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,aAAa;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAChC,UAAU;oBAAE,MAAM;gBAAK;gBACvB,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAqB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAqB;;;;;;;;;;;;;;;;;;;;;;;;AAK/C", "debugId": null}}, {"offset": {"line": 1242, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/GrainOverlay.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nexport default function GrainOverlay() {\r\n  return (\r\n    <div \r\n      className=\"fixed inset-0 pointer-events-none z-10 opacity-[0.15] mix-blend-overlay\"\r\n      style={{\r\n        backgroundImage: `url(\"data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E\")`,\r\n      }}\r\n    />\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,iBAAiB,CAAC,uTAAuT,CAAC;QAC5U;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1265, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/VignetteEffect.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\nexport default function VignetteEffect() {\r\n  return (\r\n    <div \r\n      className=\"fixed inset-0 pointer-events-none z-5\"\r\n      style={{\r\n        background: 'radial-gradient(circle at center, transparent 40%, rgba(0,0,0,0.4) 100%)',\r\n        mixBlendMode: 'multiply',\r\n      }}\r\n    />\r\n  );\r\n}"], "names": [], "mappings": ";;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QACC,WAAU;QACV,OAAO;YACL,YAAY;YACZ,cAAc;QAChB;;;;;;AAGN", "debugId": null}}, {"offset": {"line": 1289, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/ParallaxSection.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useRef } from 'react';\r\nimport { motion, useScroll, useTransform } from 'motion/react';\r\n\r\ninterface ParallaxSectionProps {\r\n  children: React.ReactNode;\r\n  speed?: number; // 0 = no parallax, 1 = normal, 2 = double speed\r\n  className?: string;\r\n}\r\n\r\nexport default function ParallaxSection({ \r\n  children, \r\n  speed = 0.5, \r\n  className = \"\" \r\n}: ParallaxSectionProps) {\r\n  const ref = useRef<HTMLDivElement>(null);\r\n  const { scrollYProgress } = useScroll({\r\n    target: ref,\r\n    offset: [\"start end\", \"end start\"]\r\n  });\r\n  \r\n  const y = useTransform(scrollYProgress, [0, 1], [0, speed * 100]);\r\n  \r\n  return (\r\n    <motion.div\r\n      ref={ref}\r\n      style={{ y }}\r\n      className={`relative ${className}`}\r\n    >\r\n      {children}\r\n    </motion.div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAWe,SAAS,gBAAgB,EACtC,QAAQ,EACR,QAAQ,GAAG,EACX,YAAY,EAAE,EACO;IACrB,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iMAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,oMAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG,QAAQ;KAAI;IAEhE,qBACE,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,OAAO;YAAE;QAAE;QACX,WAAW,CAAC,SAAS,EAAE,WAAW;kBAEjC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1336, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/components/landing/AnimatedHeroSVG.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'motion/react';\n\nexport default function AnimatedHeroSVG() {\n  return (\n    <div className=\"relative w-full h-full min-h-[400px] flex items-center justify-center\">\n      <svg\n        width=\"100%\"\n        height=\"100%\"\n        viewBox=\"0 0 600 400\"\n        className=\"max-w-lg\"\n        xmlns=\"http://www.w3.org/2000/svg\"\n      >\n        {/* Background grid */}\n        <defs>\n          <pattern id=\"grid\" width=\"40\" height=\"40\" patternUnits=\"userSpaceOnUse\">\n            <path\n              d=\"M 40 0 L 0 0 0 40\"\n              fill=\"none\"\n              stroke=\"var(--border)\"\n              strokeWidth=\"0.5\"\n              opacity=\"0.3\"\n            />\n          </pattern>\n          \n          {/* Gradient definitions */}\n          <linearGradient id=\"lineGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n            <stop offset=\"0%\" stopColor=\"#f2e9e4\" />\n            <stop offset=\"50%\" stopColor=\"#c9ada7\" />\n            <stop offset=\"100%\" stopColor=\"#9a8c98\" />\n          </linearGradient>\n          \n          <linearGradient id=\"nodeGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n            <stop offset=\"0%\" stopColor=\"#f2e9e4\" />\n            <stop offset=\"100%\" stopColor=\"#4a4e69\" />\n          </linearGradient>\n        </defs>\n        \n        {/* Grid background */}\n        <rect width=\"100%\" height=\"100%\" fill=\"url(#grid)\" />\n        \n        {/* Animated forecast line */}\n        <motion.path\n          d=\"M 50 300 Q 150 250 250 200 T 450 150 T 550 100\"\n          fill=\"none\"\n          stroke=\"url(#lineGradient)\"\n          strokeWidth=\"3\"\n          initial={{ pathLength: 0, opacity: 0 }}\n          animate={{ pathLength: 1, opacity: 1 }}\n          transition={{ duration: 2, ease: \"easeInOut\", delay: 0.5 }}\n        />\n        \n        {/* Data points */}\n        {[\n          { x: 50, y: 300, delay: 1 },\n          { x: 150, y: 250, delay: 1.2 },\n          { x: 250, y: 200, delay: 1.4 },\n          { x: 350, y: 175, delay: 1.6 },\n          { x: 450, y: 150, delay: 1.8 },\n          { x: 550, y: 100, delay: 2 },\n        ].map((point, index) => (\n          <motion.circle\n            key={index}\n            cx={point.x}\n            cy={point.y}\n            r=\"6\"\n            fill=\"url(#nodeGradient)\"\n            stroke=\"#ffffff\"\n            strokeWidth=\"2\"\n            initial={{ scale: 0, opacity: 0 }}\n            animate={{ scale: 1, opacity: 1 }}\n            transition={{ duration: 0.5, delay: point.delay }}\n            whileHover={{ scale: 1.3 }}\n          />\n        ))}\n        \n        {/* Prediction area */}\n        <motion.path\n          d=\"M 350 175 Q 450 140 550 90 Q 450 160 350 185 Z\"\n          fill=\"#f2e9e4\"\n          fillOpacity=\"0.2\"\n          stroke=\"#f2e9e4\"\n          strokeWidth=\"1\"\n          strokeDasharray=\"5,5\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 1, delay: 2.5 }}\n        />\n        \n        {/* Floating data nodes */}\n        {[\n          { x: 100, y: 80, size: 4, delay: 3 },\n          { x: 200, y: 60, size: 3, delay: 3.2 },\n          { x: 300, y: 90, size: 5, delay: 3.4 },\n          { x: 400, y: 70, size: 4, delay: 3.6 },\n          { x: 500, y: 50, size: 3, delay: 3.8 },\n        ].map((node, index) => (\n          <motion.circle\n            key={`node-${index}`}\n            cx={node.x}\n            cy={node.y}\n            r={node.size}\n            fill=\"#9a8c98\"\n            initial={{ opacity: 0, y: node.y + 20 }}\n            animate={{ \n              opacity: [0, 1, 0.7, 1],\n              y: [node.y + 20, node.y - 10, node.y + 5, node.y],\n            }}\n            transition={{ \n              duration: 2, \n              delay: node.delay,\n              repeat: Infinity,\n              repeatType: \"reverse\",\n              repeatDelay: 1\n            }}\n          />\n        ))}\n        \n        {/* AI Brain representation */}\n        <motion.g\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 1, delay: 4 }}\n        >\n          {/* Neural network connections */}\n          {[\n            { x1: 480, y1: 250, x2: 520, y2: 280 },\n            { x1: 480, y1: 250, x2: 520, y2: 220 },\n            { x1: 520, y1: 280, x2: 560, y2: 250 },\n            { x1: 520, y1: 220, x2: 560, y2: 250 },\n          ].map((line, index) => (\n            <motion.line\n              key={`connection-${index}`}\n              x1={line.x1}\n              y1={line.y1}\n              x2={line.x2}\n              y2={line.y2}\n              stroke=\"#4a4e69\"\n              strokeWidth=\"2\"\n              opacity=\"0.6\"\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 0.5, delay: 4.2 + index * 0.1 }}\n            />\n          ))}\n          \n          {/* Neural nodes */}\n          {[\n            { x: 480, y: 250 },\n            { x: 520, y: 280 },\n            { x: 520, y: 220 },\n            { x: 560, y: 250 },\n          ].map((node, index) => (\n            <motion.circle\n              key={`neural-${index}`}\n              cx={node.x}\n              cy={node.y}\n              r=\"8\"\n              fill=\"#22223b\"\n              stroke=\"#f2e9e4\"\n              strokeWidth=\"2\"\n              initial={{ scale: 0 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.3, delay: 4.5 + index * 0.1 }}\n            />\n          ))}\n        </motion.g>\n        \n        {/* Pulsing effect around AI brain */}\n        <motion.circle\n          cx=\"520\"\n          cy=\"250\"\n          r=\"40\"\n          fill=\"none\"\n          stroke=\"#f2e9e4\"\n          strokeWidth=\"1\"\n          opacity=\"0.3\"\n          initial={{ scale: 0.8, opacity: 0 }}\n          animate={{ \n            scale: [0.8, 1.2, 0.8],\n            opacity: [0, 0.3, 0]\n          }}\n          transition={{ \n            duration: 3,\n            repeat: Infinity,\n            delay: 5\n          }}\n        />\n      </svg>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,WAAU;YACV,OAAM;;8BAGN,8OAAC;;sCACC,8OAAC;4BAAQ,IAAG;4BAAO,OAAM;4BAAK,QAAO;4BAAK,cAAa;sCACrD,cAAA,8OAAC;gCACC,GAAE;gCACF,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,SAAQ;;;;;;;;;;;sCAKZ,8OAAC;4BAAe,IAAG;4BAAe,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC7D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,8OAAC;oCAAK,QAAO;oCAAM,WAAU;;;;;;8CAC7B,8OAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;sCAGhC,8OAAC;4BAAe,IAAG;4BAAe,IAAG;4BAAK,IAAG;4BAAK,IAAG;4BAAO,IAAG;;8CAC7D,8OAAC;oCAAK,QAAO;oCAAK,WAAU;;;;;;8CAC5B,8OAAC;oCAAK,QAAO;oCAAO,WAAU;;;;;;;;;;;;;;;;;;8BAKlC,8OAAC;oBAAK,OAAM;oBAAO,QAAO;oBAAO,MAAK;;;;;;8BAGtC,8OAAC,kNAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,SAAS;wBAAE,YAAY;wBAAG,SAAS;oBAAE;oBACrC,SAAS;wBAAE,YAAY;wBAAG,SAAS;oBAAE;oBACrC,YAAY;wBAAE,UAAU;wBAAG,MAAM;wBAAa,OAAO;oBAAI;;;;;;gBAI1D;oBACC;wBAAE,GAAG;wBAAI,GAAG;wBAAK,OAAO;oBAAE;oBAC1B;wBAAE,GAAG;wBAAK,GAAG;wBAAK,OAAO;oBAAI;oBAC7B;wBAAE,GAAG;wBAAK,GAAG;wBAAK,OAAO;oBAAI;oBAC7B;wBAAE,GAAG;wBAAK,GAAG;wBAAK,OAAO;oBAAI;oBAC7B;wBAAE,GAAG;wBAAK,GAAG;wBAAK,OAAO;oBAAI;oBAC7B;wBAAE,GAAG;wBAAK,GAAG;wBAAK,OAAO;oBAAE;iBAC5B,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,8OAAC,kNAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,IAAI,MAAM,CAAC;wBACX,IAAI,MAAM,CAAC;wBACX,GAAE;wBACF,MAAK;wBACL,QAAO;wBACP,aAAY;wBACZ,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BAAE,UAAU;4BAAK,OAAO,MAAM,KAAK;wBAAC;wBAChD,YAAY;4BAAE,OAAO;wBAAI;uBAVpB;;;;;8BAeT,8OAAC,kNAAA,CAAA,SAAM,CAAC,IAAI;oBACV,GAAE;oBACF,MAAK;oBACL,aAAY;oBACZ,QAAO;oBACP,aAAY;oBACZ,iBAAgB;oBAChB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,YAAY;wBAAE,UAAU;wBAAG,OAAO;oBAAI;;;;;;gBAIvC;oBACC;wBAAE,GAAG;wBAAK,GAAG;wBAAI,MAAM;wBAAG,OAAO;oBAAE;oBACnC;wBAAE,GAAG;wBAAK,GAAG;wBAAI,MAAM;wBAAG,OAAO;oBAAI;oBACrC;wBAAE,GAAG;wBAAK,GAAG;wBAAI,MAAM;wBAAG,OAAO;oBAAI;oBACrC;wBAAE,GAAG;wBAAK,GAAG;wBAAI,MAAM;wBAAG,OAAO;oBAAI;oBACrC;wBAAE,GAAG;wBAAK,GAAG;wBAAI,MAAM;wBAAG,OAAO;oBAAI;iBACtC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,kNAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,IAAI,KAAK,CAAC;wBACV,IAAI,KAAK,CAAC;wBACV,GAAG,KAAK,IAAI;wBACZ,MAAK;wBACL,SAAS;4BAAE,SAAS;4BAAG,GAAG,KAAK,CAAC,GAAG;wBAAG;wBACtC,SAAS;4BACP,SAAS;gCAAC;gCAAG;gCAAG;gCAAK;6BAAE;4BACvB,GAAG;gCAAC,KAAK,CAAC,GAAG;gCAAI,KAAK,CAAC,GAAG;gCAAI,KAAK,CAAC,GAAG;gCAAG,KAAK,CAAC;6BAAC;wBACnD;wBACA,YAAY;4BACV,UAAU;4BACV,OAAO,KAAK,KAAK;4BACjB,QAAQ;4BACR,YAAY;4BACZ,aAAa;wBACf;uBAhBK,CAAC,KAAK,EAAE,OAAO;;;;;8BAqBxB,8OAAC,kNAAA,CAAA,SAAM,CAAC,CAAC;oBACP,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAG,OAAO;oBAAE;;wBAGnC;4BACC;gCAAE,IAAI;gCAAK,IAAI;gCAAK,IAAI;gCAAK,IAAI;4BAAI;4BACrC;gCAAE,IAAI;gCAAK,IAAI;gCAAK,IAAI;gCAAK,IAAI;4BAAI;4BACrC;gCAAE,IAAI;gCAAK,IAAI;gCAAK,IAAI;gCAAK,IAAI;4BAAI;4BACrC;gCAAE,IAAI;gCAAK,IAAI;gCAAK,IAAI;gCAAK,IAAI;4BAAI;yBACtC,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,kNAAA,CAAA,SAAM,CAAC,IAAI;gCAEV,IAAI,KAAK,EAAE;gCACX,IAAI,KAAK,EAAE;gCACX,IAAI,KAAK,EAAE;gCACX,IAAI,KAAK,EAAE;gCACX,QAAO;gCACP,aAAY;gCACZ,SAAQ;gCACR,SAAS;oCAAE,YAAY;gCAAE;gCACzB,SAAS;oCAAE,YAAY;gCAAE;gCACzB,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;+BAVjD,CAAC,WAAW,EAAE,OAAO;;;;;wBAe7B;4BACC;gCAAE,GAAG;gCAAK,GAAG;4BAAI;4BACjB;gCAAE,GAAG;gCAAK,GAAG;4BAAI;4BACjB;gCAAE,GAAG;gCAAK,GAAG;4BAAI;4BACjB;gCAAE,GAAG;gCAAK,GAAG;4BAAI;yBAClB,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,8OAAC,kNAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,IAAI,KAAK,CAAC;gCACV,IAAI,KAAK,CAAC;gCACV,GAAE;gCACF,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAE;gCACpB,YAAY;oCAAE,UAAU;oCAAK,OAAO,MAAM,QAAQ;gCAAI;+BATjD,CAAC,OAAO,EAAE,OAAO;;;;;;;;;;;8BAe5B,8OAAC,kNAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,IAAG;oBACH,IAAG;oBACH,GAAE;oBACF,MAAK;oBACL,QAAO;oBACP,aAAY;oBACZ,SAAQ;oBACR,SAAS;wBAAE,OAAO;wBAAK,SAAS;oBAAE;oBAClC,SAAS;wBACP,OAAO;4BAAC;4BAAK;4BAAK;yBAAI;wBACtB,SAAS;4BAAC;4BAAG;4BAAK;yBAAE;oBACtB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO;oBACT;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1786, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/KairosAI/Kairos_t0/frontend/app/landing/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport <PERSON> from 'next/link';\r\nimport { ArrowRightIcon, ChartBarIcon, ClockIcon, CodeBracketIcon, LightBulbIcon } from '@heroicons/react/24/outline';\r\nimport { CalendarDaysIcon } from '@heroicons/react/24/solid';\r\nimport { motion } from 'motion/react';\r\nimport FontSwitcher from '../../components/FontSwitcher';\r\n\r\n// Custom components\r\nimport ModernNavbar from '../components/landing/ModernNavbar';\r\nimport LandingBackgroundBlobs from '../components/LandingBackgroundBlobs';\r\nimport DemoChart from '../components/landing/DemoChart';\r\nimport GrainOverlay from '../components/GrainOverlay';\r\nimport VignetteEffect from '../components/VignetteEffect';\r\nimport ParallaxSection from '../components/ParallaxSection';\r\nimport AnimatedHeroSVG from '../components/landing/AnimatedHeroSVG';\r\n\r\nconst BACKEND_API_URL = process.env.NEXT_PUBLIC_BACKEND_API_URL || 'http://localhost:8000';\r\n\r\nexport default function LandingPage() {\r\n  const [email, setEmail] = useState('');\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    try {\r\n      const res = await fetch(`${BACKEND_API_URL}/waitlist`, {\r\n        method: 'POST',\r\n        headers: { 'Content-Type': 'application/json' },\r\n        body: JSON.stringify({ email }),\r\n      });\r\n      if (res.ok) {\r\n        alert('Thanks for joining our waitlist! We\\'ll be in touch soon.');\r\n        setEmail('');\r\n      } else {\r\n        const data = await res.json();\r\n        alert(data.detail || 'There was an error. Please try again.');\r\n      }\r\n    } catch (err) {\r\n      alert('There was an error. Please try again.');\r\n    }\r\n  };\r\n\r\n  // Animation variants for staggered animations\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.1,\r\n        delayChildren: 0.3\r\n      }\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { y: 20, opacity: 0 },\r\n    visible: {\r\n      y: 0,\r\n      opacity: 1,\r\n      transition: { duration: 0.5 }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <FontSwitcher>\r\n      <LandingBackgroundBlobs />\r\n      <GrainOverlay />\r\n      <VignetteEffect />\r\n      <ModernNavbar />\r\n\r\n      {/* Hero Section */}\r\n      <section className=\"relative min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 py-20\">\r\n        <div className=\"max-w-7xl mx-auto w-full\">\r\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\r\n            {/* Left side - Text content */}\r\n            <motion.div\r\n              className=\"text-left\"\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              variants={containerVariants}\r\n            >\r\n              <motion.h1\r\n                className=\"text-4xl md:text-6xl font-bold mb-6 text-foreground leading-tight\"\r\n                variants={itemVariants}\r\n              >\r\n                <span className=\"text-foreground\">Agents that build world models for </span>\r\n                <span className=\"text-primary\">forecasting</span>\r\n              </motion.h1>\r\n\r\n              <motion.p\r\n                className=\"text-xl md:text-2xl text-muted mb-10 max-w-2xl\"\r\n                variants={itemVariants}\r\n              >\r\n                A powerful agentic forecasting system that goes beyond traditional time series models by intelligently gathering context and simulating scenarios, saving businesses hours of manual work and cost reduction.\r\n              </motion.p>\r\n\r\n              {/* Waitlist Form */}\r\n              <motion.div\r\n                className=\"max-w-md\"\r\n                variants={itemVariants}\r\n                id=\"waitlist\"\r\n              >\r\n                <form onSubmit={handleSubmit} className=\"flex flex-col sm:flex-row gap-3\">\r\n                  <input\r\n                    type=\"email\"\r\n                    value={email}\r\n                    onChange={(e) => setEmail(e.target.value)}\r\n                    placeholder=\"Enter your email\"\r\n                    required\r\n                    className=\"flex-grow px-4 py-3 rounded-full bg-secondary/20 border border-border text-foreground placeholder-muted focus:outline-none focus:ring-2 focus:ring-primary backdrop-blur-sm\"\r\n                  />\r\n                  <motion.button\r\n                    type=\"submit\"\r\n                    className=\"glow-button px-6 py-3 bg-primary text-foreground font-bold rounded-full hover:bg-secondary transition-colors shadow-lg border-2 border-transparent hover:border-border\"\r\n                    whileHover={{ scale: 1.05 }}\r\n                    whileTap={{ scale: 0.95 }}\r\n                  >\r\n                    Join Waitlist\r\n                  </motion.button>\r\n                </form>\r\n              </motion.div>\r\n            </motion.div>\r\n\r\n            {/* Right side - Animated SVG */}\r\n            <motion.div\r\n              className=\"flex justify-center lg:justify-end\"\r\n              initial={{ opacity: 0, x: 50 }}\r\n              animate={{ opacity: 1, x: 0 }}\r\n              transition={{ duration: 0.8, delay: 0.3 }}\r\n            >\r\n              <AnimatedHeroSVG />\r\n            </motion.div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Scroll indicator */}\r\n        <motion.div\r\n          className=\"absolute bottom-10 left-1/2 transform -translate-x-1/2\"\r\n          initial={{ opacity: 0 }}\r\n          animate={{ opacity: 1 }}\r\n          transition={{ delay: 1, duration: 1 }}\r\n        >\r\n          <motion.div\r\n            animate={{ y: [0, 10, 0] }}\r\n            transition={{ repeat: Infinity, duration: 1.5 }}\r\n            onClick={() => {\r\n              const el = document.getElementById('demo');\r\n              if (el) {\r\n                const yOffset = -40; // Less negative offset for more downward scroll\r\n                const y = el.getBoundingClientRect().top + window.pageYOffset + yOffset;\r\n                window.scrollTo({ top: y, behavior: 'smooth' });\r\n              }\r\n            }}\r\n            style={{ cursor: 'pointer' }}\r\n          >\r\n            <ArrowRightIcon className=\"h-6 w-6 text-white rotate-90\" />\r\n          </motion.div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Vision Section */}\r\n      <ParallaxSection speed={0.3}>\r\n        <section id=\"about\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\r\n          <motion.div\r\n            className=\"max-w-5xl mx-auto\"\r\n            initial={{ opacity: 0 }}\r\n            whileInView={{ opacity: 1 }}\r\n            viewport={{ once: true, amount: 0.3 }}\r\n            transition={{ duration: 0.5 }}\r\n          >\r\n            <motion.h2\r\n              className=\"text-3xl md:text-4xl font-bold mb-12 text-white text-center text-shadow-glow\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.7 }}\r\n            >\r\n              Our <span className=\"text-red-400\">Vision</span>\r\n            </motion.h2>\r\n\r\n            <motion.div\r\n              className=\"glass-card p-8 md:p-10\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.7, delay: 0.2 }}\r\n            >\r\n              <div className=\"grid md:grid-cols-2 gap-10\">\r\n                <motion.div\r\n                  initial={{ x: -50, opacity: 0 }}\r\n                  whileInView={{ x: 0, opacity: 1 }}\r\n                  viewport={{ once: true }}\r\n                  transition={{ duration: 0.7, delay: 0.4 }}\r\n                >\r\n                  <h3 className=\"text-xl text-white mb-4\">Context & Causality</h3>\r\n                  <p className=\"text-gray-300\">\r\n                    To build intelligent agents that understand context and causality to power real-world forecasting and decision-making\r\n                  </p>\r\n                </motion.div>\r\n                <motion.div\r\n                  initial={{ x: 50, opacity: 0 }}\r\n                  whileInView={{ x: 0, opacity: 1 }}\r\n                  viewport={{ once: true }}\r\n                  transition={{ duration: 0.7, delay: 0.6 }}\r\n                >\r\n                  <h3 className=\"text-xl text-primary mb-4\">Time as a Core Dimension</h3>\r\n                  <p className=\"text-muted\">\r\n                    Build a foundational time series model that treats time as a core dimension, so that machines can reason over contexts, simulate dynamic futures, and enable long-term decision intelligence.\r\n                  </p>\r\n                </motion.div>\r\n              </div>\r\n            </motion.div>\r\n          </motion.div>\r\n        </section>\r\n      </ParallaxSection>\r\n\r\n      {/* Features Section */}\r\n      <section id=\"features\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-secondary/10 backdrop-blur-sm\">\r\n        <motion.div\r\n          className=\"max-w-6xl mx-auto\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <motion.h2\r\n            className=\"text-3xl md:text-4xl font-bold mb-16 text-foreground text-center\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7 }}\r\n          >\r\n            Powerful <span className=\"text-primary\">Features</span>\r\n          </motion.h2>\r\n\r\n          <div className=\"grid md:grid-cols-1 lg:grid-cols-3 gap-8\">\r\n            {/* Feature 1 */}\r\n            <motion.div\r\n              className=\"glass-card p-6 flex flex-col h-full border-t-2 border-accent\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.1 }}\r\n              whileHover={{ y: -10, transition: { duration: 0.2 } }}\r\n            >\r\n              <div className=\"mb-4 text-accent\">\r\n                <ChartBarIcon className=\"h-10 w-10\" />\r\n              </div>\r\n              <h3 className=\"text-xl text-foreground mb-3\">Gathers and processes both structured (time series) and unstructured (text, news, web) data to generate accurate, real-world forecasts.</h3>\r\n            </motion.div>\r\n            {/* Feature 2 */}\r\n            <motion.div\r\n              className=\"glass-card p-6 flex flex-col h-full border-t-2 border-secondary\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.2 }}\r\n              whileHover={{ y: -10, transition: { duration: 0.2 } }}\r\n            >\r\n              <div className=\"mb-4 text-secondary\">\r\n                <CodeBracketIcon className=\"h-10 w-10\" />\r\n              </div>\r\n              <h3 className=\"text-xl text-foreground mb-3\">Performs causal discovery and provides transparent reasoning through explainability graphs and traceable logic.</h3>\r\n            </motion.div>\r\n            {/* Feature 3 */}\r\n            <motion.div\r\n              className=\"glass-card p-6 flex flex-col h-full border-t-2 border-primary\"\r\n              initial={{ y: 50, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.3 }}\r\n              whileHover={{ y: -10, transition: { duration: 0.2 } }}\r\n            >\r\n              <div className=\"mb-4 text-primary\">\r\n                <LightBulbIcon className=\"h-10 w-10\" />\r\n              </div>\r\n              <h3 className=\"text-xl text-foreground mb-3\">Combines time series foundation models with a user-facing dashboard for tailored insights, scenario simulations, and business-specific agent templates.</h3>\r\n            </motion.div>\r\n          </div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Demo Section */}\r\n      <section id=\"demo\" className=\"py-20 px-4 sm:px-6 lg:px-8\">\r\n        <motion.div\r\n          className=\"max-w-6xl mx-auto\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <motion.h2\r\n            className=\"text-3xl md:text-4xl font-bold mb-12 text-foreground text-center\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7 }}\r\n          >\r\n            See Kairos <span className=\"text-primary\">in Action</span>\r\n          </motion.h2>\r\n\r\n          <motion.div\r\n            className=\"glass-card p-6 md:p-10 overflow-hidden\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7, delay: 0.2 }}\r\n          >\r\n            {/* Interactive demo chart */}\r\n            <div className=\"grid  gap-8 \">\r\n              <motion.div\r\n                className=\"bg-secondary/20 rounded-lg backdrop-blur-sm\"\r\n                initial={{ x: 50, opacity: 0 }}\r\n                whileInView={{ x: 0, opacity: 1 }}\r\n                viewport={{ once: true }}\r\n                transition={{ duration: 0.7, delay: 0.4 }}\r\n                whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}\r\n              >\r\n                <DemoChart />\r\n              </motion.div>\r\n            </div>\r\n\r\n            <motion.div\r\n              className=\"mt-8 text-center\"\r\n              initial={{ y: 30, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.9 }}\r\n            >\r\n              <motion.div\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                {/* <Link href=\"/chat\" className=\"inline-block px-8 py-4 bg-red-400 text-white font-bold rounded-full hover:bg-red-500 transition-colors shadow-lg shadow-red-400/40 border-2 border-transparent hover:border-white/20 text-lg\">\r\n                  Try the Demo\r\n                </Link> */}\r\n              </motion.div>\r\n            </motion.div>\r\n          </motion.div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Talk to Founders Section */}\r\n      <section id=\"founders\" className=\"py-20 px-4 sm:px-6 lg:px-8 bg-black/30 backdrop-blur-sm\">\r\n        <motion.div\r\n          className=\"max-w-4xl mx-auto text-center\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <motion.h2\r\n            className=\"text-3xl md:text-4xl font-bold mb-6 text-white\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7 }}\r\n          >\r\n            Talk to Our <span className=\"text-red-400\">Founders</span>\r\n          </motion.h2>\r\n          <motion.p\r\n            className=\"text-xl text-white mb-10\"\r\n            initial={{ y: 30, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7, delay: 0.2 }}\r\n          >\r\n            Schedule a personalized demo and discover how Kairos can transform your credit risk assessment\r\n          </motion.p>\r\n\r\n          <motion.div\r\n            className=\"glass-card p-8 md:p-10 inline-block\"\r\n            initial={{ y: 50, opacity: 0 }}\r\n            whileInView={{ y: 0, opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7, delay: 0.3 }}\r\n            whileHover={{\r\n              y: -5,\r\n               transition: { duration: 0.2 }\r\n            }}\r\n          >\r\n            <div className=\"flex items-center justify-center gap-4\">\r\n              <motion.div\r\n                initial={{ rotate: 0 }}\r\n                animate={{ rotate: 360 }}\r\n                transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\r\n              >\r\n                <CalendarDaysIcon className=\"h-10 w-10 text-red-400\" />\r\n              </motion.div>\r\n              <motion.a\r\n                href=\"https://calendly.com/jajoo-kairosai/30min\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"px-8 py-4 bg-red-400 text-white font-bold rounded-full hover:bg-red-500 transition-colors shadow-lg shadow-red-400/30 border-2 border-transparent hover:border-white/20 text-lg\"\r\n                whileHover={{ scale: 1.05 }}\r\n                whileTap={{ scale: 0.95 }}\r\n              >\r\n                Schedule a Meeting\r\n              </motion.a>\r\n            </div>\r\n          </motion.div>\r\n\r\n          <motion.div\r\n            className=\"mt-8 text-gray-400 text-sm\"\r\n            initial={{ opacity: 0 }}\r\n            whileInView={{ opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.7, delay: 0.5 }}\r\n          >\r\n            {/* Backed by <span className=\"text-red-400 font-medium\">Y Combinator</span>} */}\r\n          </motion.div>\r\n        </motion.div>\r\n      </section>\r\n\r\n      {/* Footer */}\r\n      <footer className=\"py-12 px-4 sm:px-6 lg:px-8 border-t border-border\">\r\n        <motion.div\r\n          className=\"max-w-6xl mx-auto\"\r\n          initial={{ opacity: 0 }}\r\n          whileInView={{ opacity: 1 }}\r\n          viewport={{ once: true, amount: 0.1 }}\r\n          transition={{ duration: 0.5 }}\r\n        >\r\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\r\n            <motion.div\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.1 }}\r\n            >\r\n              <h3 className=\"text-lg text-foreground mb-4\">Kairos</h3>\r\n              <p className=\"text-muted\">\r\n              Trying to teach models what Einstein taught physics that time isn't just a label, it's a dimension!\r\n              </p>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.2 }}\r\n            >\r\n              <h3 className=\"text-lg text-foreground mb-4\">Links</h3>\r\n              <ul className=\"space-y-2\">\r\n                <li><Link href=\"/\" className=\"text-muted hover:text-primary transition-colors\">Home</Link></li>\r\n                <li><Link href=\"/about\" className=\"text-muted hover:text-primary transition-colors\">About</Link></li>\r\n                <li><Link href=\"/contact\" className=\"text-muted hover:text-primary transition-colors\">Contact</Link></li>\r\n              </ul>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.3 }}\r\n            >\r\n              <h3 className=\"text-lg text-foreground mb-4\">Legal</h3>\r\n              <ul className=\"space-y-2\">\r\n                <li><Link href=\"/privacy\" className=\"text-muted hover:text-primary transition-colors\">Privacy Policy</Link></li>\r\n                <li><Link href=\"/terms\" className=\"text-muted hover:text-primary transition-colors\">Terms of Service</Link></li>\r\n              </ul>\r\n            </motion.div>\r\n\r\n            <motion.div\r\n              initial={{ y: 20, opacity: 0 }}\r\n              whileInView={{ y: 0, opacity: 1 }}\r\n              viewport={{ once: true }}\r\n              transition={{ duration: 0.5, delay: 0.4 }}\r\n            >\r\n              <h3 className=\"text-lg  text-white mb-4\">Connect</h3>\r\n              <div className=\"flex space-x-4\">\r\n                <motion.a\r\n                  href=\"https://x.com/kairos_ai__\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-gray-400 hover:text-kairosYellow transition-colors\"\r\n                  whileHover={{ scale: 1.2, color: \"#fff68d\" }}\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\r\n                    <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\r\n                  </svg>\r\n                </motion.a>\r\n                <motion.a\r\n                  href=\"https://www.linkedin.com/company/kairos-ai/\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-gray-400 hover:text-kairosYellow transition-colors\"\r\n                  whileHover={{ scale: 1.2, color: \"#fff68d\" }}\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\r\n                    <path d=\"M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z\" />\r\n                  </svg>\r\n                </motion.a>\r\n                <motion.a\r\n                  href=\"http://www.github.com/KairosAI-IN\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"text-gray-400 hover:text-kairosYellow transition-colors\"\r\n                  whileHover={{ scale: 1.2, color: \"#fff68d\" }}\r\n                >\r\n                  <svg className=\"w-6 h-6\" fill=\"currentColor\" viewBox=\"0 0 24 24\" aria-hidden=\"true\">\r\n                    <path fillRule=\"evenodd\" d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0112 6.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0022 12.017C22 6.484 17.522 2 12 2z\" clipRule=\"evenodd\" />\r\n                  </svg>\r\n                </motion.a>\r\n              </div>\r\n            </motion.div>\r\n          </div>\r\n\r\n          <motion.div\r\n            className=\"mt-12 pt-8 border-t border-white/10 text-center\"\r\n            initial={{ opacity: 0 }}\r\n            whileInView={{ opacity: 1 }}\r\n            viewport={{ once: true }}\r\n            transition={{ duration: 0.5, delay: 0.6 }}\r\n          >\r\n            <p className=\"text-gray-400\">\r\n              &copy; {new Date().getFullYear()} Kairos AI. All rights reserved.\r\n            </p>\r\n          </motion.div>\r\n        </motion.div>\r\n      </footer>\r\n    </FontSwitcher>\r\n  );\r\n}\r\n\r\n\r\n\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAEA,oBAAoB;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;AAkBA,MAAM,kBAAkB,6DAA2C;AAEpD,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,MAAM,MAAM,MAAM,GAAG,gBAAgB,SAAS,CAAC,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAM;YAC/B;YACA,IAAI,IAAI,EAAE,EAAE;gBACV,MAAM;gBACN,SAAS;YACX,OAAO;gBACL,MAAM,OAAO,MAAM,IAAI,IAAI;gBAC3B,MAAM,KAAK,MAAM,IAAI;YACvB;QACF,EAAE,OAAO,KAAK;YACZ,MAAM;QACR;IACF;IAEA,8CAA8C;IAC9C,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBAAE,UAAU;YAAI;QAC9B;IACF;IAEA,qBACE,8OAAC,2HAAA,CAAA,UAAY;;0BACX,8OAAC,4IAAA,CAAA,UAAsB;;;;;0BACvB,8OAAC,kIAAA,CAAA,UAAY;;;;;0BACb,8OAAC,oIAAA,CAAA,UAAc;;;;;0BACf,8OAAC,6IAAA,CAAA,UAAY;;;;;0BAGb,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAQ;oCACR,SAAQ;oCACR,UAAU;;sDAEV,8OAAC,kNAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,UAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAkB;;;;;;8DAClC,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAGjC,8OAAC,kNAAA,CAAA,SAAM,CAAC,CAAC;4CACP,WAAU;4CACV,UAAU;sDACX;;;;;;sDAKD,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAU;4CACV,IAAG;sDAEH,cAAA,8OAAC;gDAAK,UAAU;gDAAc,WAAU;;kEACtC,8OAAC;wDACC,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,aAAY;wDACZ,QAAQ;wDACR,WAAU;;;;;;kEAEZ,8OAAC,kNAAA,CAAA,SAAM,CAAC,MAAM;wDACZ,MAAK;wDACL,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEACzB;;;;;;;;;;;;;;;;;;;;;;;8CAQP,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC,gJAAA,CAAA,UAAe;;;;;;;;;;;;;;;;;;;;;kCAMtB,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;4BAAG,UAAU;wBAAE;kCAEpC,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,GAAG;oCAAC;oCAAG;oCAAI;iCAAE;4BAAC;4BACzB,YAAY;gCAAE,QAAQ;gCAAU,UAAU;4BAAI;4BAC9C,SAAS;gCACP,MAAM,KAAK,SAAS,cAAc,CAAC;gCACnC,IAAI,IAAI;oCACN,MAAM,UAAU,CAAC,IAAI,gDAAgD;oCACrE,MAAM,IAAI,GAAG,qBAAqB,GAAG,GAAG,GAAG,OAAO,WAAW,GAAG;oCAChE,OAAO,QAAQ,CAAC;wCAAE,KAAK;wCAAG,UAAU;oCAAS;gCAC/C;4BACF;4BACA,OAAO;gCAAE,QAAQ;4BAAU;sCAE3B,cAAA,8OAAC,2NAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAMhC,8OAAC,qIAAA,CAAA,UAAe;gBAAC,OAAO;0BACtB,cAAA,8OAAC;oBAAQ,IAAG;oBAAQ,WAAU;8BAC5B,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,aAAa;4BAAE,SAAS;wBAAE;wBAC1B,UAAU;4BAAE,MAAM;4BAAM,QAAQ;wBAAI;wBACpC,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,8OAAC,kNAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,aAAa;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;;oCAC7B;kDACK,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAGrC,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,GAAG;oCAAI,SAAS;gCAAE;gCAC7B,aAAa;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CAExC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG,CAAC;gDAAI,SAAS;4CAAE;4CAC9B,aAAa;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAI/B,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,GAAG;gDAAI,SAAS;4CAAE;4CAC7B,aAAa;gDAAE,GAAG;gDAAG,SAAS;4CAAE;4CAChC,UAAU;gDAAE,MAAM;4CAAK;4CACvB,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;;8DAExC,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;8DAC1C,8OAAC;oDAAE,WAAU;8DAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWtC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC,kNAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,aAAa;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;4BAAI;;gCAC7B;8CACU,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAG1C,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,YAAY;wCAAE,GAAG,CAAC;wCAAI,YAAY;4CAAE,UAAU;wCAAI;oCAAE;;sDAEpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,uNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;;;;;;;8CAG/C,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,YAAY;wCAAE,GAAG,CAAC;wCAAI,YAAY;4CAAE,UAAU;wCAAI;oCAAE;;sDAEpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6NAAA,CAAA,kBAAe;gDAAC,WAAU;;;;;;;;;;;sDAE7B,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;;;;;;;8CAG/C,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;oCACxC,YAAY;wCAAE,GAAG,CAAC;wCAAI,YAAY;4CAAE,UAAU;wCAAI;oCAAE;;sDAEpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,yNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrD,8OAAC;gBAAQ,IAAG;gBAAO,WAAU;0BAC3B,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC,kNAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,aAAa;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;4BAAI;;gCAC7B;8CACY,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAG5C,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,aAAa;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;8CAGxC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,GAAG;4CAAI,SAAS;wCAAE;wCAC7B,aAAa;4CAAE,GAAG;4CAAG,SAAS;wCAAE;wCAChC,UAAU;4CAAE,MAAM;wCAAK;wCACvB,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;wCACxC,YAAY;4CAAE,OAAO;4CAAM,YAAY;gDAAE,UAAU;4CAAI;wCAAE;kDAEzD,cAAA,8OAAC,0IAAA,CAAA,UAAS;;;;;;;;;;;;;;;8CAId,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;8CAExC,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAYlC,8OAAC;gBAAQ,IAAG;gBAAW,WAAU;0BAC/B,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC,kNAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,aAAa;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;4BAAI;;gCAC7B;8CACa,8OAAC;oCAAK,WAAU;8CAAe;;;;;;;;;;;;sCAE7C,8OAAC,kNAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,aAAa;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCACzC;;;;;;sCAID,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,GAAG;gCAAI,SAAS;4BAAE;4BAC7B,aAAa;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAChC,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;4BACxC,YAAY;gCACV,GAAG,CAAC;gCACH,YAAY;oCAAE,UAAU;gCAAI;4BAC/B;sCAEA,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;wCAAE;wCACrB,SAAS;4CAAE,QAAQ;wCAAI;wCACvB,YAAY;4CAAE,UAAU;4CAAI,QAAQ;4CAAU,MAAM;wCAAS;kDAE7D,cAAA,8OAAC,6NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;;;;;;kDAE9B,8OAAC,kNAAA,CAAA,SAAM,CAAC,CAAC;wCACP,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;;;;;;;;;;;;sCAML,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;;;;;;;;;;;;;;;;;0BAQ9C,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,aAAa;wBAAE,SAAS;oBAAE;oBAC1B,UAAU;wBAAE,MAAM;wBAAM,QAAQ;oBAAI;oBACpC,YAAY;wBAAE,UAAU;oBAAI;;sCAE5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAE,WAAU;sDAAa;;;;;;;;;;;;8CAK5B,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAkD;;;;;;;;;;;8DAC/E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAkD;;;;;;;;;;;8DACpF,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAkD;;;;;;;;;;;;;;;;;;;;;;;8CAI1F,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAA+B;;;;;;sDAC7C,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAkD;;;;;;;;;;;8DACtF,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAkD;;;;;;;;;;;;;;;;;;;;;;;8CAIxF,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,aAAa;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAChC,UAAU;wCAAE,MAAM;oCAAK;oCACvB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAI;;sDAExC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kNAAA,CAAA,SAAM,CAAC,CAAC;oDACP,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;oDACV,YAAY;wDAAE,OAAO;wDAAK,OAAO;oDAAU;8DAE3C,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;wDAAY,eAAY;kEAC3E,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC,kNAAA,CAAA,SAAM,CAAC,CAAC;oDACP,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;oDACV,YAAY;wDAAE,OAAO;wDAAK,OAAO;oDAAU;8DAE3C,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;wDAAY,eAAY;kEAC3E,cAAA,8OAAC;4DAAK,GAAE;;;;;;;;;;;;;;;;8DAGZ,8OAAC,kNAAA,CAAA,SAAM,CAAC,CAAC;oDACP,MAAK;oDACL,QAAO;oDACP,KAAI;oDACJ,WAAU;oDACV,YAAY;wDAAE,OAAO;wDAAK,OAAO;oDAAU;8DAE3C,cAAA,8OAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAe,SAAQ;wDAAY,eAAY;kEAC3E,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAmtB,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjwB,8OAAC,kNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,aAAa;gCAAE,SAAS;4BAAE;4BAC1B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC;gCAAE,WAAU;;oCAAgB;oCACnB,IAAI,OAAO,WAAW;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}]}